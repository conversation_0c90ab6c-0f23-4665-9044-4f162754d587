{% extends "base.html" %}

{% block title %}Auto-Mining Dashboard - Onnyx Platform{% endblock %}

{% block head %}
<style>
    .mining-status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 8px;
    }
    
    .mining-active {
        background: #22c55e;
        box-shadow: 0 0 10px rgba(34, 197, 94, 0.5);
        animation: pulse 2s infinite;
    }
    
    .mining-inactive {
        background: #6b7280;
    }
    
    .mining-scheduled {
        background: #f59e0b;
        box-shadow: 0 0 10px rgba(245, 158, 11, 0.5);
    }
    
    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
    }
    
    .stats-card {
        background: var(--glass-bg);
        backdrop-filter: blur(12px);
        -webkit-backdrop-filter: blur(12px);
        border: 1px solid var(--glass-border);
        border-radius: 16px;
        padding: 2rem;
        transition: all 0.3s ease;
    }
    
    .stats-card:hover {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(0, 212, 255, 0.3);
        box-shadow: 0 0 20px rgba(0, 212, 255, 0.2);
    }
    
    .validator-card {
        background: var(--glass-bg);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid var(--glass-border);
        border-radius: 12px;
        padding: 1.5rem;
        transition: all 0.3s ease;
    }
    
    .validator-card:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    }
    
    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 60px;
        height: 34px;
    }
    
    .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }
    
    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #6b7280;
        transition: 0.4s;
        border-radius: 34px;
    }
    
    .slider:before {
        position: absolute;
        content: "";
        height: 26px;
        width: 26px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        transition: 0.4s;
        border-radius: 50%;
    }
    
    input:checked + .slider {
        background-color: var(--cyber-cyan);
        box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
    }
    
    input:checked + .slider:before {
        transform: translateX(26px);
    }
</style>
{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
            <div>
                <h1 class="text-4xl font-orbitron font-bold text-cyber-cyan mb-2">
                    🤖 Auto-Mining Dashboard
                </h1>
                <p class="text-xl text-text-secondary">
                    Automated mining management for your validators
                </p>
            </div>
            
            <!-- System Controls -->
            <div class="mt-4 md:mt-0 flex space-x-4">
                {% if summary_stats.system_running %}
                <button onclick="stopAutoMining()" 
                        class="glass-button px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105"
                        style="background: rgba(239, 68, 68, 0.2); border-color: rgba(239, 68, 68, 0.3); color: #ef4444;">
                    ⏹️ Stop System
                </button>
                {% else %}
                <button onclick="startAutoMining()" 
                        class="glass-button-primary px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                    🚀 Start System
                </button>
                {% endif %}
                
                <a href="{{ url_for('auto_mining.performance') }}" 
                   class="glass-button px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                    📊 Performance
                </a>
                
                <a href="{{ url_for('auto_mining.settings') }}" 
                   class="glass-button px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                    ⚙️ Settings
                </a>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="stats-card text-center">
            <div class="text-3xl font-orbitron font-bold text-cyber-cyan mb-2">
                {{ summary_stats.total_validators }}
            </div>
            <div class="text-sm text-text-tertiary uppercase tracking-wider">Total Validators</div>
        </div>
        
        <div class="stats-card text-center">
            <div class="text-3xl font-orbitron font-bold text-green-400 mb-2">
                {{ summary_stats.active_miners }}
            </div>
            <div class="text-sm text-text-tertiary uppercase tracking-wider">Active Miners</div>
        </div>
        
        <div class="stats-card text-center">
            <div class="text-3xl font-orbitron font-bold text-cyber-purple mb-2">
                {{ "%.2f"|format(summary_stats.total_earnings) }}
            </div>
            <div class="text-sm text-text-tertiary uppercase tracking-wider">Total Earnings</div>
        </div>
        
        <div class="stats-card text-center">
            <div class="text-3xl font-orbitron font-bold text-cyber-blue mb-2">
                {{ summary_stats.total_blocks }}
            </div>
            <div class="text-sm text-text-tertiary uppercase tracking-wider">Blocks Mined</div>
        </div>
    </div>

    <!-- System Status -->
    <div class="glass-card p-6 mb-8">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="mining-status-indicator {% if summary_stats.system_running %}mining-active{% else %}mining-inactive{% endif %}"></div>
                <div>
                    <h3 class="text-xl font-orbitron font-bold text-white">
                        Auto-Mining System
                    </h3>
                    <p class="text-text-tertiary">
                        {% if summary_stats.system_running %}
                        System is running and monitoring validators
                        {% else %}
                        System is stopped
                        {% endif %}
                    </p>
                </div>
            </div>
            
            <div class="text-right">
                <div class="text-sm text-text-tertiary">Last Updated</div>
                <div class="text-cyber-cyan font-mono" id="last-updated">
                    {{ moment().format('HH:mm:ss') }}
                </div>
            </div>
        </div>
    </div>

    <!-- Validators List -->
    <div class="glass-card p-6">
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-2xl font-orbitron font-bold text-cyber-cyan">
                Validator Auto-Mining Status
            </h2>
            <button onclick="refreshStatus()" 
                    class="glass-button px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300">
                🔄 Refresh
            </button>
        </div>

        {% if validators %}
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {% for validator in validators %}
            <div class="validator-card">
                <div class="flex items-start justify-between mb-4">
                    <div class="flex-1">
                        <div class="flex items-center space-x-3 mb-2">
                            <div class="mining-status-indicator 
                                {% if validator.currently_mining %}mining-active
                                {% elif validator.auto_mining_enabled and not validator.in_mining_hours %}mining-scheduled
                                {% else %}mining-inactive{% endif %}">
                            </div>
                            <h3 class="text-lg font-orbitron font-bold text-white">
                                {{ validator.name }}
                            </h3>
                        </div>
                        <p class="text-sm text-text-tertiary uppercase tracking-wider">
                            {{ validator.category }} • {{ validator.mining_tier|title }} Tier
                        </p>
                    </div>
                    
                    <!-- Auto-Mining Toggle -->
                    <label class="toggle-switch">
                        <input type="checkbox" 
                               {% if validator.auto_mining_enabled %}checked{% endif %}
                               onchange="toggleValidator('{{ validator.sela_id }}')">
                        <span class="slider"></span>
                    </label>
                </div>

                <!-- Validator Stats -->
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div>
                        <div class="text-sm text-text-tertiary">Mining Power</div>
                        <div class="text-cyber-cyan font-mono">{{ validator.mining_power }}x</div>
                    </div>
                    <div>
                        <div class="text-sm text-text-tertiary">Blocks Mined</div>
                        <div class="text-green-400 font-mono">{{ validator.blocks_mined or 0 }}</div>
                    </div>
                    <div>
                        <div class="text-sm text-text-tertiary">Earnings</div>
                        <div class="text-cyber-purple font-mono">{{ "%.2f"|format(validator.mining_rewards_earned or 0) }}</div>
                    </div>
                    <div>
                        <div class="text-sm text-text-tertiary">Uptime</div>
                        <div class="text-cyber-blue font-mono">{{ validator.uptime }}</div>
                    </div>
                </div>

                <!-- Status Information -->
                <div class="space-y-2 mb-4">
                    {% if validator.currently_mining %}
                    <div class="flex items-center space-x-2">
                        <span class="text-green-400">●</span>
                        <span class="text-sm text-green-400">Currently Mining</span>
                        {% if validator.process_id %}
                        <span class="text-xs text-text-tertiary">(PID: {{ validator.process_id }})</span>
                        {% endif %}
                    </div>
                    {% elif validator.auto_mining_enabled %}
                        {% if validator.in_mining_hours %}
                        <div class="flex items-center space-x-2">
                            <span class="text-yellow-400">●</span>
                            <span class="text-sm text-yellow-400">Starting Soon</span>
                        </div>
                        {% else %}
                        <div class="flex items-center space-x-2">
                            <span class="text-blue-400">●</span>
                            <span class="text-sm text-blue-400">Scheduled (Outside Hours)</span>
                        </div>
                        {% endif %}
                    {% else %}
                    <div class="flex items-center space-x-2">
                        <span class="text-gray-400">●</span>
                        <span class="text-sm text-gray-400">Auto-Mining Disabled</span>
                    </div>
                    {% endif %}
                    
                    {% if validator.restart_attempts > 0 %}
                    <div class="flex items-center space-x-2">
                        <span class="text-orange-400">⚠️</span>
                        <span class="text-sm text-orange-400">{{ validator.restart_attempts }} restart(s)</span>
                    </div>
                    {% endif %}
                    
                    {% if validator.schedule_enabled %}
                    <div class="flex items-center space-x-2">
                        <span class="text-purple-400">⏰</span>
                        <span class="text-sm text-purple-400">Scheduled Mining</span>
                    </div>
                    {% endif %}
                </div>

                <!-- Action Buttons -->
                <div class="flex space-x-2">
                    <a href="{{ url_for('auto_mining.configure_validator', sela_id=validator.sela_id) }}" 
                       class="flex-1 glass-button px-3 py-2 rounded-lg text-sm font-medium text-center transition-all duration-300">
                        ⚙️ Configure
                    </a>
                    <a href="{{ url_for('sela.profile', sela_id=validator.sela_id) }}" 
                       class="flex-1 glass-button px-3 py-2 rounded-lg text-sm font-medium text-center transition-all duration-300">
                        📊 Details
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-12">
            <div class="text-6xl mb-4">🤖</div>
            <h3 class="text-xl font-orbitron font-bold text-text-secondary mb-2">
                No Validators Found
            </h3>
            <p class="text-text-tertiary mb-6">
                Register a validator to start auto-mining
            </p>
            <a href="{{ url_for('auth.register_sela') }}" 
               class="glass-button-primary px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300">
                🏢 Register Validator
            </a>
        </div>
        {% endif %}
    </div>
</div>

<script>
// Auto-refresh status every 30 seconds
setInterval(refreshStatus, 30000);

function refreshStatus() {
    fetch('/auto-mining/api/status')
        .then(response => response.json())
        .then(data => {
            // Update last updated time
            document.getElementById('last-updated').textContent = new Date().toLocaleTimeString();
            
            // Optionally reload the page for full update
            // location.reload();
        })
        .catch(error => {
            console.error('Error refreshing status:', error);
        });
}

function startAutoMining() {
    if (confirm('Start the auto-mining system?')) {
        fetch('/auto-mining/api/start', { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Failed to start auto-mining: ' + data.error);
                }
            })
            .catch(error => {
                alert('Error starting auto-mining: ' + error);
            });
    }
}

function stopAutoMining() {
    if (confirm('Stop the auto-mining system? This will stop all active miners.')) {
        fetch('/auto-mining/api/stop', { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Failed to stop auto-mining: ' + data.error);
                }
            })
            .catch(error => {
                alert('Error stopping auto-mining: ' + error);
            });
    }
}

function toggleValidator(selaId) {
    fetch(`/auto-mining/api/toggle/${selaId}`, { method: 'POST' })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Optionally show a success message
                console.log('Validator toggled:', data.message);
                // Refresh after a short delay to see changes
                setTimeout(() => location.reload(), 1000);
            } else {
                alert('Failed to toggle validator: ' + data.error);
                // Revert the toggle
                location.reload();
            }
        })
        .catch(error => {
            alert('Error toggling validator: ' + error);
            location.reload();
        });
}
</script>
{% endblock %}
