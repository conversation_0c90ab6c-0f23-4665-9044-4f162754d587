"""
Onnyx Block Validator Demo

This script demonstrates how to use the block validator module to validate
blocks and chains in a real-world scenario.
"""

import sys
import os
import json
import time
import hashlib
import uuid
from typing import Dict, List, Any

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Add the VM directory to the Python path
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "vm"))

from block_validator import (
    validate_block,
    validate_block_safe,
    validate_chain,
    validate_block_structure,
    validate_block_hash,
    validate_block_transactions,
    validate_block_miner_reward,
    validate_block_against_chain,
    BlockValidationError
)

from validator import (
    validate_transaction,
    validate_transaction_safe,
    validate_transactions,
    ValidationError
)

from opcodes import (
    OP_MINT, OP_SEND, OP_IDENTITY, OP_SCROLL,
    identity, selas, tokens, ledger
)

def setup_demo_data():
    """Set up demo data for the block validator demo."""
    print("Setting up demo data...")
    
    # Set up identities
    identity.identities["alice"] = {
        "name": "Alice",
        "public_key": "0x123456789abcdef",
        "created_at": int(time.time())
    }
    
    identity.identities["bob"] = {
        "name": "Bob",
        "public_key": "0x987654321fedcba",
        "created_at": int(time.time())
    }
    
    # Set up badges
    identity.badges["alice"] = ["CREATOR", "VALIDATOR"]
    identity.badges["bob"] = ["CREATOR"]
    
    # Set up Selas
    selas.selas["alice"] = {
        "name": "Alice's Business",
        "type": "service",
        "tier": "gold",
        "created_at": int(time.time())
    }
    
    # Set up tokens
    tokens.tokens["ALICE"] = {
        "symbol": "ALICE",
        "name": "Alice Token",
        "creator": "alice",
        "supply": 10000,
        "created_at": int(time.time())
    }
    
    # Set up ledger
    ledger.balances["alice:ALICE"] = 10000
    
    print("Demo data setup complete.")

def create_transaction(op: str, from_id: str = None, data: Dict[str, Any] = None) -> Dict[str, Any]:
    """Create a transaction with the given parameters."""
    if data is None:
        data = {}
    
    tx = {
        "op": op,
        "txid": hashlib.sha256(f"{op}-{from_id}-{uuid.uuid4()}".encode()).hexdigest()[:16],
        "data": data
    }
    
    if from_id:
        tx["from"] = from_id
    
    return tx

def create_block(index: int, previous_hash: str, transactions: List[Dict[str, Any]] = None, timestamp: int = None) -> Dict[str, Any]:
    """Create a block with the given parameters."""
    if transactions is None:
        transactions = []
    
    if timestamp is None:
        timestamp = int(time.time())
    
    # Create block without hash
    block = {
        "index": index,
        "timestamp": timestamp,
        "transactions": transactions,
        "previous_hash": previous_hash
    }
    
    # Calculate hash
    block_string = json.dumps(block, sort_keys=True)
    block["hash"] = hashlib.sha256(block_string.encode()).hexdigest()
    
    return block

def demo_block_creation():
    """Demonstrate block creation and validation."""
    print("\n=== Block Creation and Validation ===")
    
    # Create a genesis block
    genesis_block = create_block(0, "")
    
    print("Created Genesis Block:")
    print(f"  Index: {genesis_block['index']}")
    print(f"  Hash: {genesis_block['hash']}")
    print(f"  Previous Hash: {genesis_block['previous_hash']}")
    print(f"  Timestamp: {genesis_block['timestamp']}")
    print(f"  Transactions: {len(genesis_block['transactions'])}")
    
    # Validate the genesis block
    try:
        validate_block(genesis_block, None, False)
        print("Genesis block is valid.")
    except BlockValidationError as e:
        print(f"Genesis block validation error: {e}")
    
    # Create a coinbase transaction for the next block
    coinbase_tx = create_transaction(OP_MINT, None, {
        "symbol": "ONX",
        "supply": 50
    })
    
    # Create a regular transaction
    regular_tx = create_transaction(OP_SEND, "alice", {
        "token_id": "ALICE",
        "to": "bob",
        "amount": 100
    })
    
    # Create the next block
    block1 = create_block(1, genesis_block["hash"], [coinbase_tx, regular_tx], genesis_block["timestamp"] + 60)
    
    print("\nCreated Block 1:")
    print(f"  Index: {block1['index']}")
    print(f"  Hash: {block1['hash']}")
    print(f"  Previous Hash: {block1['previous_hash']}")
    print(f"  Timestamp: {block1['timestamp']}")
    print(f"  Transactions: {len(block1['transactions'])}")
    
    # Validate the block
    try:
        validate_block(block1, genesis_block)
        print("Block 1 is valid.")
    except BlockValidationError as e:
        print(f"Block 1 validation error: {e}")
    
    return genesis_block, block1

def demo_chain_validation(genesis_block, block1):
    """Demonstrate chain validation."""
    print("\n=== Chain Validation ===")
    
    # Create a coinbase transaction for the next block
    coinbase_tx = create_transaction(OP_MINT, None, {
        "symbol": "ONX",
        "supply": 50
    })
    
    # Create a regular transaction
    regular_tx = create_transaction(OP_IDENTITY, None, {
        "identity_id": "charlie",
        "name": "Charlie",
        "public_key": "0xabcdef123456789"
    })
    
    # Create the next block
    block2 = create_block(2, block1["hash"], [coinbase_tx, regular_tx], block1["timestamp"] + 60)
    
    print("Created Block 2:")
    print(f"  Index: {block2['index']}")
    print(f"  Hash: {block2['hash']}")
    print(f"  Previous Hash: {block2['previous_hash']}")
    print(f"  Timestamp: {block2['timestamp']}")
    print(f"  Transactions: {len(block2['transactions'])}")
    
    # Create the chain
    chain = [genesis_block, block1, block2]
    
    # Validate the chain
    try:
        validate_chain(chain)
        print("Chain is valid.")
    except BlockValidationError as e:
        print(f"Chain validation error: {e}")
    
    return chain

def demo_invalid_block(chain):
    """Demonstrate invalid block detection."""
    print("\n=== Invalid Block Detection ===")
    
    # Create an invalid block (wrong index)
    invalid_block = create_block(4, chain[-1]["hash"], [], chain[-1]["timestamp"] + 60)
    
    print("Created Invalid Block (wrong index):")
    print(f"  Index: {invalid_block['index']}")
    print(f"  Hash: {invalid_block['hash']}")
    print(f"  Previous Hash: {invalid_block['previous_hash']}")
    
    # Validate the block
    is_valid, error = validate_block_safe(invalid_block, chain[-1])
    
    print(f"Is valid: {is_valid}")
    if not is_valid:
        print(f"Error: {error}")
    
    # Create an invalid block (tampered transaction)
    coinbase_tx = create_transaction(OP_MINT, None, {
        "symbol": "ONX",
        "supply": 100  # Should be 50
    })
    
    invalid_block2 = create_block(3, chain[-1]["hash"], [coinbase_tx], chain[-1]["timestamp"] + 60)
    
    print("\nCreated Invalid Block (invalid miner reward):")
    print(f"  Index: {invalid_block2['index']}")
    print(f"  Hash: {invalid_block2['hash']}")
    print(f"  Previous Hash: {invalid_block2['previous_hash']}")
    
    # Validate the block
    is_valid, error = validate_block_safe(invalid_block2, chain[-1])
    
    print(f"Is valid: {is_valid}")
    if not is_valid:
        print(f"Error: {error}")

def demo_block_mining():
    """Demonstrate block mining."""
    print("\n=== Block Mining ===")
    
    # Create a genesis block
    genesis_block = create_block(0, "")
    
    # Create a mempool of transactions
    mempool = [
        create_transaction(OP_SEND, "alice", {
            "token_id": "ALICE",
            "to": "bob",
            "amount": 50
        }),
        create_transaction(OP_IDENTITY, None, {
            "identity_id": "charlie",
            "name": "Charlie",
            "public_key": "0xabcdef123456789"
        }),
        create_transaction(OP_SEND, "alice", {
            "token_id": "ALICE",
            "to": "bob",
            "amount": 100
        })
    ]
    
    print(f"Mempool contains {len(mempool)} transactions")
    
    # Validate transactions in the mempool
    valid_txs, invalid_txs = validate_transactions(mempool)
    
    print(f"Valid transactions: {len(valid_txs)}")
    print(f"Invalid transactions: {len(invalid_txs)}")
    
    # Create a coinbase transaction
    coinbase_tx = create_transaction(OP_MINT, None, {
        "symbol": "ONX",
        "supply": 50
    })
    
    # Create a new block with the coinbase transaction and valid transactions
    new_block = create_block(1, genesis_block["hash"], [coinbase_tx] + valid_txs, genesis_block["timestamp"] + 60)
    
    print("\nMined new block:")
    print(f"  Index: {new_block['index']}")
    print(f"  Hash: {new_block['hash']}")
    print(f"  Previous Hash: {new_block['previous_hash']}")
    print(f"  Timestamp: {new_block['timestamp']}")
    print(f"  Transactions: {len(new_block['transactions'])}")
    
    # Validate the new block
    try:
        validate_block(new_block, genesis_block)
        print("New block is valid and can be added to the chain.")
    except BlockValidationError as e:
        print(f"New block validation error: {e}")

def main():
    """Run the block validator demo."""
    print("Onnyx Block Validator Demo")
    print("==========================")
    
    # Set up demo data
    setup_demo_data()
    
    # Run demo scenarios
    genesis_block, block1 = demo_block_creation()
    chain = demo_chain_validation(genesis_block, block1)
    demo_invalid_block(chain)
    demo_block_mining()
    
    print("\nDemo completed.")

if __name__ == "__main__":
    main()
