"""
Test script for the Onnyx Block Validator.

This script tests the block validator module for validating blocks and chains.
"""

import sys
import os
import json
import time
import hashlib
from typing import Dict, List, Any

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Add the VM directory to the Python path
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "vm"))

from block_validator import (
    validate_block,
    validate_block_safe,
    validate_chain,
    validate_block_structure,
    validate_block_hash,
    validate_block_transactions,
    validate_block_miner_reward,
    validate_block_against_chain,
    BlockValidationError
)

from opcodes import (
    OP_MINT, OP_SEND, OP_IDENTITY, OP_SCROLL,
    identity, selas, tokens, ledger
)

def setup_test_data():
    """Set up test data for the block validator tests."""
    # Set up identities
    identity.identities["id1"] = {"name": "Test Identity 1"}
    identity.identities["id2"] = {"name": "Test Identity 2"}
    identity.badges["id1"] = ["CREATOR"]

    # Set up Selas
    selas.selas["id1"] = {"name": "Test Sela 1"}

    # Set up tokens
    tokens.tokens["TKN"] = {"symbol": "TKN", "supply": 1000}

    # Set up ledger
    ledger.balances["id1:TKN"] = 500

def create_test_block(index: int, previous_hash: str, transactions: List[Dict[str, Any]] = None, timestamp: int = None) -> Dict[str, Any]:
    """Create a test block with the given parameters."""
    if transactions is None:
        transactions = []

    if timestamp is None:
        timestamp = int(time.time())

    # Create block without hash
    block = {
        "index": index,
        "timestamp": timestamp,
        "transactions": transactions,
        "previous_hash": previous_hash
    }

    # Calculate hash
    block_string = json.dumps(block, sort_keys=True)
    block["hash"] = hashlib.sha256(block_string.encode()).hexdigest()

    return block

def create_test_transaction(op: str, from_id: str = None, data: Dict[str, Any] = None) -> Dict[str, Any]:
    """Create a test transaction with the given parameters."""
    if data is None:
        data = {}

    tx = {
        "op": op,
        "txid": hashlib.sha256(f"{op}-{from_id}-{time.time()}".encode()).hexdigest()[:16],
        "data": data
    }

    if from_id:
        tx["from"] = from_id

    return tx

def test_validate_block_structure():
    """Test the validate_block_structure function."""
    print("\nTesting validate_block_structure...")

    # Create a valid block
    valid_block = create_test_block(0, "")

    try:
        result = validate_block_structure(valid_block)
        print(f"Valid block structure: {result}")
    except BlockValidationError as e:
        print(f"Error: {e}")

    # Test invalid block (missing field)
    invalid_block = valid_block.copy()
    del invalid_block["timestamp"]

    try:
        result = validate_block_structure(invalid_block)
        print(f"Invalid block structure (should not reach here): {result}")
    except BlockValidationError as e:
        print(f"Expected error: {e}")

    # Test invalid block (wrong field type)
    invalid_block2 = valid_block.copy()
    invalid_block2["index"] = "0"  # Should be an integer

    try:
        result = validate_block_structure(invalid_block2)
        print(f"Invalid block structure (should not reach here): {result}")
    except BlockValidationError as e:
        print(f"Expected error: {e}")

def test_validate_block_hash():
    """Test the validate_block_hash function."""
    print("\nTesting validate_block_hash...")

    # Create a valid block
    valid_block = create_test_block(0, "")

    try:
        result = validate_block_hash(valid_block)
        print(f"Valid block hash: {result}")
    except BlockValidationError as e:
        print(f"Error: {e}")

    # Test invalid block (tampered hash)
    invalid_block = valid_block.copy()
    invalid_block["hash"] = "invalid_hash"

    try:
        result = validate_block_hash(invalid_block)
        print(f"Invalid block hash (should not reach here): {result}")
    except BlockValidationError as e:
        print(f"Expected error: {e}")

def test_validate_block_transactions():
    """Test the validate_block_transactions function."""
    print("\nTesting validate_block_transactions...")

    # Create valid transactions
    tx1 = create_test_transaction(OP_SEND, "id1", {
        "token_id": "TKN",
        "to": "id2",
        "amount": 100
    })

    tx2 = create_test_transaction(OP_IDENTITY, None, {
        "identity_id": "id3",
        "name": "Test Identity 3",
        "public_key": "0x123456789abcdef"
    })

    # Create a valid block with transactions
    valid_block = create_test_block(0, "", [tx1, tx2])

    try:
        result = validate_block_transactions(valid_block)
        print(f"Valid block transactions: {result}")
    except BlockValidationError as e:
        print(f"Error: {e}")

    # Test invalid block (duplicate transactions)
    invalid_block = create_test_block(0, "", [tx1, tx1])

    try:
        result = validate_block_transactions(invalid_block)
        print(f"Invalid block transactions (should not reach here): {result}")
    except BlockValidationError as e:
        print(f"Expected error: {e}")

    # Test invalid block (invalid transaction)
    invalid_tx = create_test_transaction(OP_SEND, "id3", {  # id3 doesn't exist
        "token_id": "TKN",
        "to": "id2",
        "amount": 100
    })

    invalid_block2 = create_test_block(0, "", [tx1, invalid_tx])

    try:
        result = validate_block_transactions(invalid_block2)
        print(f"Invalid block transactions (should not reach here): {result}")
    except BlockValidationError as e:
        print(f"Expected error: {e}")

def test_validate_block_miner_reward():
    """Test the validate_block_miner_reward function."""
    print("\nTesting validate_block_miner_reward...")

    # Create a valid coinbase transaction (without 'from' field)
    coinbase_tx = {
        "op": OP_MINT,
        "txid": hashlib.sha256(f"{OP_MINT}-coinbase-{time.time()}".encode()).hexdigest()[:16],
        "data": {
            "symbol": "ONX",
            "supply": 50
        }
    }

    # Create a valid block with coinbase transaction
    valid_block = create_test_block(1, "prev_hash", [coinbase_tx])

    try:
        result = validate_block_miner_reward(valid_block)
        print(f"Valid miner reward: {result}")
    except BlockValidationError as e:
        print(f"Error: {e}")

    # Test invalid block (wrong reward amount)
    invalid_coinbase_tx = {
        "op": OP_MINT,
        "txid": hashlib.sha256(f"{OP_MINT}-coinbase-{time.time()}".encode()).hexdigest()[:16],
        "data": {
            "symbol": "ONX",
            "supply": 100  # Should be 50
        }
    }

    invalid_block = create_test_block(1, "prev_hash", [invalid_coinbase_tx])

    try:
        result = validate_block_miner_reward(invalid_block)
        print(f"Invalid miner reward (should not reach here): {result}")
    except BlockValidationError as e:
        print(f"Expected error: {e}")

    # Test invalid block (non-coinbase first transaction)
    invalid_block2 = create_test_block(1, "prev_hash", [
        create_test_transaction(OP_SEND, "id1", {
            "token_id": "TKN",
            "to": "id2",
            "amount": 100
        })
    ])

    try:
        result = validate_block_miner_reward(invalid_block2)
        print(f"Invalid miner reward (should not reach here): {result}")
    except BlockValidationError as e:
        print(f"Expected error: {e}")

def test_validate_block_against_chain():
    """Test the validate_block_against_chain function."""
    print("\nTesting validate_block_against_chain...")

    # Create a previous block with a specific timestamp
    current_time = int(time.time())
    prev_block = create_test_block(0, "", timestamp=current_time)

    # Create a valid next block with a later timestamp
    valid_block = create_test_block(1, prev_block["hash"], timestamp=current_time + 60)

    try:
        result = validate_block_against_chain(valid_block, prev_block)
        print(f"Valid block against chain: {result}")
    except BlockValidationError as e:
        print(f"Error: {e}")

    # Test invalid block (wrong index)
    invalid_block = create_test_block(2, prev_block["hash"], timestamp=current_time + 120)  # Should be 1

    try:
        result = validate_block_against_chain(invalid_block, prev_block)
        print(f"Invalid block against chain (should not reach here): {result}")
    except BlockValidationError as e:
        print(f"Expected error: {e}")

    # Test invalid block (wrong previous_hash)
    invalid_block2 = create_test_block(1, "wrong_hash", timestamp=current_time + 180)

    try:
        result = validate_block_against_chain(invalid_block2, prev_block)
        print(f"Invalid block against chain (should not reach here): {result}")
    except BlockValidationError as e:
        print(f"Expected error: {e}")

def test_validate_block():
    """Test the validate_block function."""
    print("\nTesting validate_block...")

    # Create a genesis block with a specific timestamp
    current_time = int(time.time())
    genesis_block = create_test_block(0, "", timestamp=current_time)

    try:
        result = validate_block(genesis_block, None, False)
        print(f"Valid genesis block: {result}")
    except BlockValidationError as e:
        print(f"Error: {e}")

    # Create a valid next block with a proper coinbase transaction and later timestamp
    coinbase_tx = {
        "op": OP_MINT,
        "txid": hashlib.sha256(f"{OP_MINT}-coinbase-{time.time()}".encode()).hexdigest()[:16],
        "data": {
            "symbol": "ONX",
            "supply": 50
        }
    }

    valid_block = create_test_block(1, genesis_block["hash"], [coinbase_tx], timestamp=current_time + 60)

    try:
        result = validate_block(valid_block, genesis_block)
        print(f"Valid block: {result}")
    except BlockValidationError as e:
        print(f"Error: {e}")

    # Test invalid block (wrong index)
    invalid_coinbase_tx = {
        "op": OP_MINT,
        "txid": hashlib.sha256(f"{OP_MINT}-coinbase-{time.time()}".encode()).hexdigest()[:16],
        "data": {
            "symbol": "ONX",
            "supply": 50
        }
    }

    invalid_block = create_test_block(2, genesis_block["hash"], [invalid_coinbase_tx], timestamp=current_time + 120)

    try:
        result = validate_block(invalid_block, genesis_block)
        print(f"Invalid block (should not reach here): {result}")
    except BlockValidationError as e:
        print(f"Expected error: {e}")

def test_validate_chain():
    """Test the validate_chain function."""
    print("\nTesting validate_chain...")

    # Create a chain of blocks with specific timestamps
    current_time = int(time.time())
    genesis_block = create_test_block(0, "", timestamp=current_time)

    # Create proper coinbase transactions
    coinbase_tx1 = {
        "op": OP_MINT,
        "txid": hashlib.sha256(f"{OP_MINT}-coinbase1-{time.time()}".encode()).hexdigest()[:16],
        "data": {
            "symbol": "ONX",
            "supply": 50
        }
    }

    coinbase_tx2 = {
        "op": OP_MINT,
        "txid": hashlib.sha256(f"{OP_MINT}-coinbase2-{time.time()}".encode()).hexdigest()[:16],
        "data": {
            "symbol": "ONX",
            "supply": 50
        }
    }

    # Create blocks with proper coinbase transactions and increasing timestamps
    block1 = create_test_block(1, genesis_block["hash"], [coinbase_tx1], timestamp=current_time + 60)
    block2 = create_test_block(2, block1["hash"], [coinbase_tx2], timestamp=current_time + 120)

    valid_chain = [genesis_block, block1, block2]

    try:
        result = validate_chain(valid_chain, check_miner_reward=True)
        print(f"Valid chain: {result}")
    except BlockValidationError as e:
        print(f"Error: {e}")

    # Test invalid chain (wrong order)
    invalid_chain = [genesis_block, block2, block1]

    try:
        result = validate_chain(invalid_chain)
        print(f"Invalid chain (should not reach here): {result}")
    except BlockValidationError as e:
        print(f"Expected error: {e}")

def main():
    """Run all tests."""
    print("Testing Onnyx Block Validator")
    print("============================")

    # Set up test data
    setup_test_data()

    # Run tests
    test_validate_block_structure()
    test_validate_block_hash()
    test_validate_block_transactions()
    test_validate_block_miner_reward()
    test_validate_block_against_chain()
    test_validate_block()
    test_validate_chain()

    print("\nAll tests completed.")

if __name__ == "__main__":
    main()
