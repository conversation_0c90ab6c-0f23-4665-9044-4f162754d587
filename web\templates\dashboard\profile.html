{% extends "base.html" %}

{% block title %}Profile Settings - ONNYX Dashboard{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-onyx-black via-onyx-dark to-onyx-black">
    <!-- Enhanced Header Section -->
    <section class="pt-24 pb-16">
        <div class="container-xl mx-auto px-6">
            <div class="text-center mb-16">
                <div class="flex justify-center mb-8">
                    <div class="glass-card-premium w-20 h-20 rounded-2xl flex items-center justify-center">
                        <svg class="w-10 h-10 text-cyber-cyan" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </div>
                </div>
                <h1 class="text-5xl md:text-6xl font-orbitron font-bold text-cyber-cyan mb-6">
                    Profile Settings
                </h1>
                <p class="text-xl text-text-secondary max-w-3xl mx-auto leading-relaxed">
                    Manage your identity, covenant status, and biblical tokenomics preferences
                </p>

                <!-- Profile Status Indicators -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mt-12 max-w-4xl mx-auto">
                    <div class="glass-card-enhanced p-6 text-center">
                        <div class="w-12 h-12 mx-auto mb-3 bg-gradient-to-br from-cyber-green to-green-400 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="text-lg font-orbitron font-bold text-cyber-green mb-1">Verified</div>
                        <div class="text-sm text-text-tertiary">Identity Status</div>
                    </div>

                    <div class="glass-card-enhanced p-6 text-center">
                        <div class="w-12 h-12 mx-auto mb-3 bg-gradient-to-br from-cyber-purple to-cyber-blue rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                        </div>
                        <div class="text-lg font-orbitron font-bold text-cyber-purple mb-1">{{ identity.metadata_parsed.get('cipp_tier', 'Tier 0') }}</div>
                        <div class="text-sm text-text-tertiary">CIPP Level</div>
                    </div>

                    <div class="glass-card-enhanced p-6 text-center">
                        <div class="w-12 h-12 mx-auto mb-3 bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                            </svg>
                        </div>
                        <div class="text-lg font-orbitron font-bold text-cyber-cyan mb-1">{{ identity.metadata_parsed.get('etzem_score', '0') }}</div>
                        <div class="text-sm text-text-tertiary">Etzem Score</div>
                    </div>

                    <div class="glass-card-enhanced p-6 text-center">
                        <div class="w-12 h-12 mx-auto mb-3 bg-gradient-to-br from-cyber-yellow to-cyber-orange rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="text-lg font-orbitron font-bold text-cyber-yellow mb-1">{{ identity.metadata_parsed.get('biblical_nation', 'Unassigned') }}</div>
                        <div class="text-sm text-text-tertiary">Biblical Nation</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Profile Content -->
    <section class="pb-32">
        <div class="container-xl mx-auto px-6">
            <div class="max-w-6xl mx-auto space-y-16">

                <!-- Biblical Tokenomics Profile Section -->
                <div class="glass-card-premium p-8">
                    <div class="flex items-center justify-between mb-8">
                        <div>
                            <h2 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-2">📜 Biblical Tokenomics Profile</h2>
                            <p class="text-text-secondary">Your covenant status and biblical economic participation</p>
                        </div>
                        <a href="{{ url_for('tokenomics.overview') }}"
                           class="glass-button-enhanced px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                            View Details →
                        </a>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        <!-- Yovel Status -->
                        <div class="glass-card-enhanced p-6 text-center">
                            <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-2xl flex items-center justify-center">
                                <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="text-2xl font-orbitron font-bold text-cyber-cyan mb-2">{{ biblical_data.user_yovel_tokens|round(2) if biblical_data else '0.00' }}</div>
                            <div class="text-sm text-text-secondary mb-3">Yovel Tokens Earned</div>
                            <div class="text-xs text-text-tertiary">
                                Cycle {{ biblical_data.current_yovel_cycle if biblical_data else '0' }} •
                                {{ biblical_data.years_until_reset if biblical_data else '0' }} years until reset
                            </div>
                        </div>

                        <!-- Covenant Compliance -->
                        <div class="glass-card-enhanced p-6 text-center">
                            <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-cyber-purple to-cyber-blue rounded-2xl flex items-center justify-center">
                                <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="text-2xl font-orbitron font-bold text-cyber-purple mb-2">{{ identity.metadata_parsed.get('covenant_compliance', '85') }}%</div>
                            <div class="text-sm text-text-secondary mb-3">Covenant Compliance</div>
                            <div class="text-xs {{ 'text-cyber-green' if (identity.metadata_parsed.get('covenant_compliance', 85)|int) >= 80 else 'text-cyber-yellow' if (identity.metadata_parsed.get('covenant_compliance', 85)|int) >= 60 else 'text-cyber-red' }}">
                                {{ 'Excellent' if (identity.metadata_parsed.get('covenant_compliance', 85)|int) >= 80 else 'Good' if (identity.metadata_parsed.get('covenant_compliance', 85)|int) >= 60 else 'Needs Improvement' }}
                            </div>
                        </div>

                        <!-- Gleaning Contributions -->
                        <div class="glass-card-enhanced p-6 text-center">
                            <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-cyber-green to-green-400 rounded-2xl flex items-center justify-center">
                                <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                </svg>
                            </div>
                            <div class="text-2xl font-orbitron font-bold text-cyber-green mb-2">{{ identity.metadata_parsed.get('gleaning_contributions', '12.5') }}</div>
                            <div class="text-sm text-text-secondary mb-3">ONX Contributed</div>
                            <div class="text-xs text-text-tertiary">
                                {{ identity.metadata_parsed.get('gleaning_count', '3') }} contributions this season
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Identity Profile Card -->
                <div class="glass-card-enhanced p-8">
                    <div class="flex items-center justify-between mb-8">
                        <div>
                            <h2 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-2">🔐 Identity Information</h2>
                            <p class="text-text-secondary">Update your basic identity information</p>
                        </div>
                    </div>

                    <form method="POST" action="{{ url_for('dashboard.update_profile') }}" class="space-y-8">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                            <!-- Name Field -->
                            <div class="space-y-2">
                                <label for="name" class="block text-sm font-medium text-text-secondary mb-3">
                                    Full Name *
                                </label>
                                <input type="text"
                                       id="name"
                                       name="name"
                                       value="{{ identity.name }}"
                                       required
                                       maxlength="100"
                                       class="w-full h-12 px-6 glass-card border border-glass-border rounded-xl text-white placeholder-text-tertiary focus:border-cyber-cyan focus:ring-2 focus:ring-cyber-cyan/20 transition-all duration-300 font-orbitron">
                            </div>

                            <!-- Email Field -->
                            <div class="space-y-2">
                                <label for="email" class="block text-sm font-medium text-text-secondary mb-3">
                                    Email Address *
                                </label>
                                <input type="email"
                                       id="email"
                                       name="email"
                                       value="{{ identity.email }}"
                                       required
                                       maxlength="255"
                                       class="w-full h-12 px-6 glass-card border border-glass-border rounded-xl text-white placeholder-text-tertiary focus:border-cyber-cyan focus:ring-2 focus:ring-cyber-cyan/20 transition-all duration-300 font-orbitron">
                            </div>
                        </div>

                            <!-- Read-only Identity Information -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 pt-4 border-t border-gray-700">
                                <div>
                                    <label class="block text-sm font-medium text-gray-400 mb-2">
                                        Identity ID
                                    </label>
                                    <div class="px-4 py-3 bg-gray-900/50 border border-gray-700 rounded-lg text-gray-300 font-mono text-sm">
                                        {{ identity.identity_id[:16] }}...
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-400 mb-2">
                                        Status
                                    </label>
                                    <div class="px-4 py-3 bg-gray-900/50 border border-gray-700 rounded-lg">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            {{ identity.status.title() }}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- Role Information -->
                            {% if identity.metadata_parsed.get('role') %}
                            <div>
                                <label class="block text-sm font-medium text-gray-400 mb-2">
                                    Role
                                </label>
                                <div class="px-4 py-3 bg-gray-900/50 border border-gray-700 rounded-lg text-gray-300">
                                    {{ identity.metadata_parsed.role }}
                                    {% if identity.metadata_parsed.get('genesis_identity') %}
                                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                            Genesis Identity
                                        </span>
                                    {% endif %}
                                </div>
                            </div>
                            {% endif %}

                        <!-- Submit Button -->
                        <div class="flex justify-end pt-8">
                            <button type="submit"
                                    class="glass-button-primary px-8 py-4 rounded-xl font-orbitron font-bold transition-all duration-300 hover:scale-105">
                                Update Identity
                            </button>
                        </div>
                    </form>
                </div>

                <!-- CIPP (Covenant Identity Protection Protocol) Settings -->
                <div class="glass-card-enhanced p-8">
                    <div class="flex items-center justify-between mb-8">
                        <div>
                            <h2 class="text-3xl font-orbitron font-bold text-cyber-purple mb-2">🛡️ CIPP Settings</h2>
                            <p class="text-text-secondary">Covenant Identity Protection Protocol configuration</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 rounded-full bg-cyber-green animate-pulse"></div>
                            <span class="text-sm font-orbitron text-cyber-green">ACTIVE</span>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <!-- Current CIPP Tier -->
                        <div class="glass-card p-6 text-center">
                            <div class="w-12 h-12 mx-auto mb-3 bg-gradient-to-br from-cyber-purple to-cyber-blue rounded-xl flex items-center justify-center">
                                <svg class="w-6 h-6 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.5-4L21 8.5l-7 7-3-3"></path>
                                </svg>
                            </div>
                            <div class="text-2xl font-orbitron font-bold text-cyber-purple mb-1">{{ identity.metadata_parsed.get('cipp_tier', 'Tier 0') }}</div>
                            <div class="text-sm text-text-tertiary">Current Tier</div>
                        </div>

                        <!-- Etzem Score -->
                        <div class="glass-card p-6 text-center">
                            <div class="w-12 h-12 mx-auto mb-3 bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-xl flex items-center justify-center">
                                <svg class="w-6 h-6 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                </svg>
                            </div>
                            <div class="text-2xl font-orbitron font-bold text-cyber-cyan mb-1">{{ identity.metadata_parsed.get('etzem_score', '0') }}</div>
                            <div class="text-sm text-text-tertiary">Etzem Score</div>
                        </div>

                        <!-- Biblical Nation -->
                        <div class="glass-card p-6 text-center">
                            <div class="w-12 h-12 mx-auto mb-3 bg-gradient-to-br from-cyber-yellow to-cyber-orange rounded-xl flex items-center justify-center">
                                <svg class="w-6 h-6 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="text-lg font-orbitron font-bold text-cyber-yellow mb-1">{{ identity.metadata_parsed.get('biblical_nation', 'Unassigned') }}</div>
                            <div class="text-sm text-text-tertiary">Biblical Nation</div>
                        </div>

                        <!-- Protection Status -->
                        <div class="glass-card p-6 text-center">
                            <div class="w-12 h-12 mx-auto mb-3 bg-gradient-to-br from-cyber-green to-green-400 rounded-xl flex items-center justify-center">
                                <svg class="w-6 h-6 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                </svg>
                            </div>
                            <div class="text-lg font-orbitron font-bold text-cyber-green mb-1">Protected</div>
                            <div class="text-sm text-text-tertiary">Vault Status</div>
                        </div>
                    </div>

                    <!-- CIPP Actions -->
                    <div class="flex flex-wrap gap-4 justify-center">
                        <a href="{{ url_for('cipp.upgrade_tier') }}"
                           class="glass-button-enhanced px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                            🔺 Upgrade Tier
                        </a>
                        <a href="{{ url_for('cipp.covenant_scroll') }}"
                           class="glass-button px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                            📜 View Covenant Scroll
                        </a>
                        <a href="{{ url_for('cipp.protection_settings') }}"
                           class="glass-button px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                            🛡️ Protection Settings
                        </a>
                    </div>
                </div>

                <!-- Mining Preferences -->
                <div class="glass-card-enhanced p-8">
                    <div class="flex items-center justify-between mb-8">
                        <div>
                            <h2 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-2">⛏️ Mining Preferences</h2>
                            <p class="text-text-secondary">Configure your mining behavior and biblical compliance</p>
                        </div>
                    </div>

                    <form method="POST" action="{{ url_for('dashboard.update_mining_preferences') }}" class="space-y-8">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                            <!-- Default Mining Intensity -->
                            <div class="space-y-3">
                                <label for="default_intensity" class="block text-sm font-medium text-text-secondary">
                                    Default Mining Intensity
                                </label>
                                <select id="default_intensity" name="default_intensity"
                                        class="w-full h-12 px-6 glass-card border border-glass-border rounded-xl text-white focus:border-cyber-cyan focus:ring-2 focus:ring-cyber-cyan/20 transition-all duration-300 font-orbitron">
                                    <option value="low" {{ 'selected' if identity.metadata_parsed.get('default_mining_intensity') == 'low' else '' }}>Low (Energy Efficient)</option>
                                    <option value="medium" {{ 'selected' if identity.metadata_parsed.get('default_mining_intensity', 'medium') == 'medium' else '' }}>Medium (Balanced)</option>
                                    <option value="high" {{ 'selected' if identity.metadata_parsed.get('default_mining_intensity') == 'high' else '' }}>High (Maximum Performance)</option>
                                </select>
                            </div>

                            <!-- Sabbath Compliance -->
                            <div class="space-y-3">
                                <label class="block text-sm font-medium text-text-secondary">
                                    Sabbath Compliance
                                </label>
                                <div class="flex items-center space-x-4">
                                    <label class="flex items-center space-x-3 cursor-pointer">
                                        <input type="radio" name="sabbath_compliance" value="strict"
                                               {{ 'checked' if identity.metadata_parsed.get('sabbath_compliance', 'strict') == 'strict' else '' }}
                                               class="w-4 h-4 text-cyber-purple bg-transparent border-glass-border focus:ring-cyber-purple focus:ring-2">
                                        <span class="text-white font-orbitron">Strict (Auto-stop)</span>
                                    </label>
                                    <label class="flex items-center space-x-3 cursor-pointer">
                                        <input type="radio" name="sabbath_compliance" value="flexible"
                                               {{ 'checked' if identity.metadata_parsed.get('sabbath_compliance') == 'flexible' else '' }}
                                               class="w-4 h-4 text-cyber-purple bg-transparent border-glass-border focus:ring-cyber-purple focus:ring-2">
                                        <span class="text-white font-orbitron">Flexible</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Auto-Mining Schedule -->
                        <div class="space-y-3">
                            <label class="block text-sm font-medium text-text-secondary">
                                Auto-Mining Schedule
                            </label>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                <div class="glass-card p-4 text-center">
                                    <label class="flex flex-col items-center space-y-2 cursor-pointer">
                                        <input type="checkbox" name="mining_days" value="weekdays"
                                               {{ 'checked' if 'weekdays' in identity.metadata_parsed.get('mining_schedule', []) else '' }}
                                               class="w-4 h-4 text-cyber-cyan bg-transparent border-glass-border rounded focus:ring-cyber-cyan focus:ring-2">
                                        <span class="text-sm font-orbitron text-white">Weekdays</span>
                                    </label>
                                </div>
                                <div class="glass-card p-4 text-center">
                                    <label class="flex flex-col items-center space-y-2 cursor-pointer">
                                        <input type="checkbox" name="mining_days" value="weekends"
                                               {{ 'checked' if 'weekends' in identity.metadata_parsed.get('mining_schedule', []) else '' }}
                                               class="w-4 h-4 text-cyber-cyan bg-transparent border-glass-border rounded focus:ring-cyber-cyan focus:ring-2">
                                        <span class="text-sm font-orbitron text-white">Weekends</span>
                                    </label>
                                </div>
                                <div class="glass-card p-4 text-center">
                                    <label class="flex flex-col items-center space-y-2 cursor-pointer">
                                        <input type="checkbox" name="mining_days" value="sabbath_observer"
                                               {{ 'checked' if identity.metadata_parsed.get('sabbath_observer', False) else '' }}
                                               class="w-4 h-4 text-cyber-purple bg-transparent border-glass-border rounded focus:ring-cyber-purple focus:ring-2">
                                        <span class="text-sm font-orbitron text-white">Sabbath Observer</span>
                                    </label>
                                </div>
                                <div class="glass-card p-4 text-center">
                                    <label class="flex flex-col items-center space-y-2 cursor-pointer">
                                        <input type="checkbox" name="mining_days" value="yovel_aware"
                                               {{ 'checked' if identity.metadata_parsed.get('yovel_aware', True) else '' }}
                                               class="w-4 h-4 text-cyber-yellow bg-transparent border-glass-border rounded focus:ring-cyber-yellow focus:ring-2">
                                        <span class="text-sm font-orbitron text-white">Yovel Aware</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="flex justify-end pt-8">
                            <button type="submit"
                                    class="glass-button-primary px-8 py-4 rounded-xl font-orbitron font-bold transition-all duration-300 hover:scale-105">
                                Update Mining Preferences
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Business Profile Card (if user has a Sela) -->
                {% if user_sela %}
                <div class="card">
                    <div class="card-header">
                        <h2 class="text-2xl font-bold text-white font-orbitron flex items-center">
                            <svg class="w-6 h-6 mr-3 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                            Business Validator Profile
                        </h2>
                        <p class="text-gray-300 mt-2">Manage your business information and services</p>
                    </div>

                    <div class="card-body">
                        <form method="POST" action="{{ url_for('dashboard.update_business_profile') }}" class="space-y-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Business Name -->
                                <div>
                                    <label for="business_name" class="block text-sm font-medium text-gray-300 mb-2">
                                        Business Name *
                                    </label>
                                    <input type="text"
                                           id="business_name"
                                           name="business_name"
                                           value="{{ user_sela.name }}"
                                           required
                                           maxlength="100"
                                           class="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all duration-300">
                                </div>

                                <!-- Category -->
                                <div>
                                    <label for="category" class="block text-sm font-medium text-gray-300 mb-2">
                                        Business Category *
                                    </label>
                                    <select id="category"
                                            name="category"
                                            required
                                            class="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all duration-300">
                                        <option value="">Select Category</option>
                                        <option value="services" {% if user_sela.category == 'services' %}selected{% endif %}>Services</option>
                                        <option value="healthcare" {% if user_sela.category == 'healthcare' %}selected{% endif %}>Healthcare</option>
                                        <option value="technology" {% if user_sela.category == 'technology' %}selected{% endif %}>Technology</option>
                                        <option value="retail" {% if user_sela.category == 'retail' %}selected{% endif %}>Retail</option>
                                        <option value="food" {% if user_sela.category == 'food' %}selected{% endif %}>Food & Beverage</option>
                                        <option value="education" {% if user_sela.category == 'education' %}selected{% endif %}>Education</option>
                                        <option value="finance" {% if user_sela.category == 'finance' %}selected{% endif %}>Finance</option>
                                        <option value="other" {% if user_sela.category == 'other' %}selected{% endif %}>Other</option>
                                    </select>
                                </div>
                            </div>

                            <!-- Description -->
                            <div>
                                <label for="description" class="block text-sm font-medium text-gray-300 mb-2">
                                    Business Description
                                </label>
                                <textarea id="description"
                                          name="description"
                                          rows="3"
                                          maxlength="500"
                                          placeholder="Describe your business and what you offer..."
                                          class="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all duration-300">{{ user_sela.description or '' }}</textarea>
                            </div>

                            <!-- Contact Information -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Phone -->
                                <div>
                                    <label for="phone" class="block text-sm font-medium text-gray-300 mb-2">
                                        Phone Number
                                    </label>
                                    <input type="tel"
                                           id="phone"
                                           name="phone"
                                           value="{{ user_sela.phone or '' }}"
                                           placeholder="(*************"
                                           class="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all duration-300">
                                </div>

                                <!-- Website -->
                                <div>
                                    <label for="website" class="block text-sm font-medium text-gray-300 mb-2">
                                        Website
                                    </label>
                                    <input type="url"
                                           id="website"
                                           name="website"
                                           value="{{ user_sela.website or '' }}"
                                           placeholder="https://yourbusiness.com"
                                           class="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all duration-300">
                                </div>
                            </div>

                            <!-- Address -->
                            <div>
                                <label for="address" class="block text-sm font-medium text-gray-300 mb-2">
                                    Business Address
                                </label>
                                <input type="text"
                                       id="address"
                                       name="address"
                                       value="{{ user_sela.address or '' }}"
                                       placeholder="123 Business St, City, State 12345"
                                       maxlength="255"
                                       class="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all duration-300">
                            </div>

                            <!-- Services -->
                            <div>
                                <label for="services" class="block text-sm font-medium text-gray-300 mb-2">
                                    Services Offered
                                </label>
                                <textarea id="services"
                                          name="services"
                                          rows="3"
                                          maxlength="500"
                                          placeholder="List the services you provide..."
                                          class="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-purple-400 focus:ring-2 focus:ring-purple-400/20 transition-all duration-300">{{ user_sela.services or '' }}</textarea>
                            </div>

                            <!-- Business Stats (Read-only) -->
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 pt-6 border-t border-gray-700">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-cyan-400">{{ user_sela.trust_score or 0 }}</div>
                                    <div class="text-sm text-gray-400">Trust Score</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-purple-400">{{ user_sela.mining_tier or 'Basic' }}</div>
                                    <div class="text-sm text-gray-400">Mining Tier</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-green-400">{{ "%.2f"|format(user_sela.onx_balance or 0) }}</div>
                                    <div class="text-sm text-gray-400">ONX Balance</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-yellow-400">{{ user_sela.blocks_mined or 0 }}</div>
                                    <div class="text-sm text-gray-400">Blocks Mined</div>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="flex justify-end pt-6">
                                <button type="submit"
                                        class="px-8 py-3 bg-gradient-to-r from-purple-500 to-cyan-600 text-white font-semibold rounded-lg hover:from-purple-600 hover:to-cyan-700 focus:ring-4 focus:ring-purple-400/20 transition-all duration-300 transform hover:scale-105">
                                    Update Business Profile
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                {% endif %}

                <!-- Security Information Card -->
                <div class="card">
                    <div class="card-header">
                        <h2 class="text-2xl font-bold text-white font-orbitron flex items-center">
                            <svg class="w-6 h-6 mr-3 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                            Security Information
                        </h2>
                        <p class="text-gray-300 mt-2">Your cryptographic identity details</p>
                    </div>

                    <div class="card-body">
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-400 mb-2">
                                    Public Key
                                </label>
                                <div class="px-4 py-3 bg-gray-900/50 border border-gray-700 rounded-lg text-gray-300 font-mono text-sm break-all">
                                    {{ identity.public_key[:64] }}...
                                </div>
                            </div>

                            <div class="bg-yellow-900/20 border border-yellow-600/30 rounded-lg p-4">
                                <div class="flex items-start">
                                    <svg class="w-5 h-5 text-yellow-400 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                    </svg>
                                    <div>
                                        <h4 class="text-yellow-400 font-semibold mb-1">Security Notice</h4>
                                        <p class="text-yellow-200 text-sm">
                                            Your private key is stored securely and cannot be changed through this interface.
                                            Keep your private key file safe as it controls your identity and assets.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </section>
</div>

<!-- Profile Update Success Animation -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth transitions to form elements
    const inputs = document.querySelectorAll('input, textarea, select');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('transform', 'scale-105');
        });

        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('transform', 'scale-105');
        });
    });

    // Form validation feedback
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Updating...';
                submitBtn.disabled = true;
            }
        });
    });
});
</script>
{% endblock %}
