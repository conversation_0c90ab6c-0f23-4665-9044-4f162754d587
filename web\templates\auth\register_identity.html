{% extends "base.html" %}

{% block title %}Cryptographic Identity Creation - ONNYX Platform{% endblock %}

{% block content %}
<div class="min-h-screen hero-gradient cyber-grid relative py-20">
    <!-- Floating particles -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute top-1/4 left-1/4 w-2 h-2 bg-cyber-cyan rounded-full animate-ping opacity-70"></div>
        <div class="absolute top-1/3 right-1/3 w-1 h-1 bg-cyber-purple rounded-full animate-pulse opacity-60"></div>
        <div class="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-cyber-blue rounded-full animate-bounce opacity-50"></div>
    </div>

    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="glass-card p-10 neuro-card">
            <div class="text-center mb-12">
                <!-- ONNYX Logo -->
                <div class="mb-8 flex justify-center">
                    <div class="w-16 h-16 md:w-20 md:h-20 rounded-2xl flex items-center justify-center shadow-2xl shadow-cyber-cyan/30 bg-white/5 backdrop-blur-sm border border-white/20 hover:shadow-cyber-cyan/50 transition-all duration-500 group">
                        <img src="{{ url_for('static', filename='images/onnyx_logo.png') }}"
                             alt="ONNYX Logo"
                             class="w-12 h-12 md:w-16 md:h-16 object-contain group-hover:scale-110 transition-all duration-500"
                             style="filter: brightness(0) saturate(100%) invert(64%) sepia(100%) saturate(2000%) hue-rotate(180deg) brightness(100%) contrast(100%);">
                    </div>
                </div>

                <div class="w-20 h-20 bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-xl flex items-center justify-center mx-auto mb-6 glow-effect">
                    <svg class="w-10 h-10 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                </div>
                <h1 class="text-4xl md:text-5xl font-orbitron font-bold mb-4">
                    <span class="hologram-text">Covenant Identity Creation</span>
                </h1>
                <p class="text-xl text-gray-300 max-w-2xl mx-auto leading-relaxed">
                    Join the biblical covenant community with quantum-resistant digital sovereignty
                </p>
            </div>

            <form method="POST" x-data="identityForm()" @submit="handleSubmit" class="space-y-8">
                <!-- Name Field -->
                <div class="space-y-3">
                    <label for="name" class="block text-sm font-orbitron font-semibold text-cyber-cyan mb-3">
                        🔐 Full Name *
                    </label>
                    <input type="text"
                           id="name"
                           name="name"
                           required
                           x-model="form.name"
                           class="form-input w-full"
                           placeholder="Enter your full name">
                    <p class="text-xs text-gray-500 font-mono uppercase tracking-wider">This will be your public display name on the network</p>
                </div>

                <!-- Email Field -->
                <div class="space-y-3">
                    <label for="email" class="block text-sm font-orbitron font-semibold text-cyber-cyan mb-3">
                        📧 Email Address *
                    </label>
                    <input type="email"
                           id="email"
                           name="email"
                           required
                           x-model="form.email"
                           @blur="validateEmail"
                           class="form-input w-full"
                           placeholder="Enter your email address">
                    <div x-show="emailValidation.checking" class="text-xs text-cyber-blue mt-2 flex items-center space-x-2">
                        <div class="spinner"></div>
                        <span>Checking availability...</span>
                    </div>
                    <div x-show="emailValidation.message"
                         :class="emailValidation.valid ? 'text-green-400' : 'text-red-400'"
                         class="text-xs mt-2 font-mono"
                         x-text="emailValidation.message">
                    </div>
                </div>

                <!-- Role Selection -->
                <div class="space-y-3">
                    <label for="role" class="block text-sm font-orbitron font-semibold text-cyber-cyan mb-3">
                        👤 Covenant Role Class
                    </label>
                    <select id="role"
                            name="role"
                            x-model="form.role"
                            class="form-select w-full">
                        <option value="Citizen">👥 Citizen</option>
                        <option value="Harashim">🔧 Harashim (Craftsperson)</option>
                        <option value="Sela_Founder">🏢 Sela Founder</option>
                        <option value="Elder">👴 Elder</option>
                        <option value="Priest">⚖️ Priest</option>
                        <option value="Warrior">⚔️ Warrior</option>
                        <option value="Council_Member">🏛️ Council Member</option>
                    </select>
                    <p class="text-xs text-gray-500 font-mono uppercase tracking-wider">Your role in the covenant community</p>
                </div>

                <!-- Nation of Origin Selection -->
                <div class="space-y-3">
                    <label for="nation_of_origin" class="block text-sm font-orbitron font-semibold text-cyber-cyan mb-3">
                        🏛️ Nation of Origin *
                    </label>
                    <select id="nation_of_origin"
                            name="nation_of_origin"
                            x-model="form.nation_of_origin"
                            required
                            class="form-select w-full">
                        {% for nation in biblical_nations %}
                        <option value="{{ nation.nation_code }}">
                            {{ nation.flag_symbol }} {{ nation.nation_name }} ({{ nation.tribe_name }})
                        </option>
                        {% endfor %}
                    </select>
                    <p class="text-xs text-gray-500 font-mono uppercase tracking-wider">Choose your biblical tribal affiliation</p>
                </div>

                <!-- Key Generation Info -->
                <div class="glass-card p-6 border border-cyber-cyan/30 bg-cyber-cyan/10">
                    <div class="flex items-start space-x-4">
                        <div class="w-8 h-8 bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-lg flex items-center justify-center flex-shrink-0">
                            <svg class="w-5 h-5 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-orbitron font-bold text-cyber-cyan mb-2">🔑 Quantum-Resistant Cryptography</h3>
                            <p class="text-gray-300 leading-relaxed">
                                We'll automatically generate ECDSA cryptographic key pairs for your identity.
                                Your private key will be provided after registration - store it securely in multiple locations!
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Covenant Acceptance -->
                <div class="glass-card p-6 border border-cyber-purple/30 bg-cyber-purple/10">
                    <div class="flex items-start space-x-4 mb-6">
                        <div class="w-8 h-8 bg-gradient-to-br from-cyber-purple to-cyber-blue rounded-lg flex items-center justify-center flex-shrink-0">
                            <span class="text-xl">📜</span>
                        </div>
                        <div>
                            <h3 class="text-lg font-orbitron font-bold text-cyber-purple mb-2">Digital Scroll of Rights</h3>
                            <p class="text-gray-300 leading-relaxed">
                                By joining ONNYX, you enter a covenant community with protected rights including
                                digital anonymity, private commerce, and protection against unjust exile.
                            </p>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <a href="{{ url_for('auth.covenant_scroll') }}?from=registration"
                           target="_blank"
                           class="glass-button w-full px-4 py-3 rounded-lg font-medium text-center block hover:scale-105 transition-all duration-300">
                            📜 Read the Digital Scroll of Rights
                        </a>

                        <label class="flex items-start space-x-4 cursor-pointer group">
                            <input type="checkbox"
                                   name="covenant_accepted"
                                   required
                                   x-model="form.covenantAccepted"
                                   class="form-checkbox mt-1">
                            <span class="text-gray-300 leading-relaxed group-hover:text-white transition-colors duration-300">
                                I have read and accept the <strong class="text-cyber-purple">Digital Scroll of Rights</strong>
                                and covenant to uphold the principles of digital sovereignty, economic justice,
                                and biblical commerce within the ONNYX network.
                            </span>
                        </label>
                    </div>
                </div>

                <!-- Terms and Conditions -->
                <div class="space-y-4">
                    <label class="flex items-start space-x-4 cursor-pointer group">
                        <input type="checkbox"
                               required
                               x-model="form.acceptTerms"
                               class="form-checkbox mt-1">
                        <span class="text-gray-300 leading-relaxed group-hover:text-white transition-colors duration-300">
                            I understand that I will receive cryptographic keys that I must keep secure,
                            and I agree to the platform's terms of service and privacy policy. I acknowledge
                            that lost private keys cannot be recovered.
                        </span>
                    </label>
                </div>

                <!-- Submit Button -->
                <div class="space-y-4">
                    <button type="submit"
                            :disabled="!canSubmit"
                            :class="canSubmit ? 'glass-button-primary hover:scale-105' : 'glass-card opacity-50 cursor-not-allowed'"
                            class="w-full px-8 py-4 rounded-xl font-orbitron font-bold transition-all duration-300">
                        <span x-show="!submitting" class="flex items-center justify-center space-x-3">
                            <span>🚀 Initialize Identity</span>
                        </span>
                        <span x-show="submitting" class="flex items-center justify-center space-x-3">
                            <div class="spinner"></div>
                            <span>Generating Cryptographic Keys...</span>
                        </span>
                    </button>
                </div>

                <!-- Security Notice -->
                <div class="glass-card p-6 border border-yellow-500/30 bg-yellow-500/10">
                    <div class="flex items-start space-x-4">
                        <div class="w-8 h-8 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-lg flex items-center justify-center flex-shrink-0">
                            <svg class="w-5 h-5 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-orbitron font-bold text-yellow-400 mb-2">⚠️ Critical Security Protocol</h3>
                            <p class="text-yellow-200 leading-relaxed">
                                Your private key will be generated and displayed only once during this process.
                                Download and store it in multiple secure locations immediately. The ONNYX network
                                cannot recover lost private keys - you are the sole custodian of your cryptographic identity.
                            </p>
                        </div>
                    </div>
                </div>
            </form>

            <!-- Back Link -->
            <div class="mt-12 text-center">
                <a href="{{ url_for('register_choice') }}"
                   class="glass-button px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                    ← Return to Verification Portal
                </a>
            </div>
        </div>
    </div>
</div>

<script>
function identityForm() {
    return {
        form: {
            name: '',
            email: '',
            role: 'Citizen',
            nation_of_origin: 'JU',
            covenantAccepted: false,
            acceptTerms: false
        },
        emailValidation: {
            valid: null,
            message: '',
            checking: false
        },
        submitting: false,

        get canSubmit() {
            return this.form.name &&
                   this.form.email &&
                   this.form.nation_of_origin &&
                   this.form.covenantAccepted &&
                   this.form.acceptTerms &&
                   this.emailValidation.valid !== false &&
                   !this.submitting;
        },

        async validateEmail() {
            if (!this.form.email) return;

            this.emailValidation.checking = true;
            this.emailValidation.message = '';

            try {
                const response = await fetch('/api/validate/email', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email: this.form.email })
                });

                const result = await response.json();
                this.emailValidation.valid = result.valid;
                this.emailValidation.message = result.message;
            } catch (error) {
                this.emailValidation.valid = false;
                this.emailValidation.message = 'Validation failed';
            } finally {
                this.emailValidation.checking = false;
            }
        },

        handleSubmit(event) {
            if (!this.canSubmit) {
                event.preventDefault();
                return;
            }
            this.submitting = true;
        }
    }
}
</script>
{% endblock %}
