"""
Dashboard Routes

User dashboard and management interface.
"""

import os
import sys
import json
import time
import uuid
import logging
from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for, flash

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from shared.models.identity import Identity
from shared.db.db import db

logger = logging.getLogger("onnyx.web.dashboard")

dashboard_bp = Blueprint('dashboard', __name__)

def require_auth(f):
    """Decorator to require authentication."""
    from functools import wraps

    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'identity_id' not in session:
            flash('Please log in to access this page.', 'warning')
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    return decorated_function

@dashboard_bp.route('/')
@require_auth
def overview():
    """Dashboard overview page."""
    try:
        identity_id = session['identity_id']

        # Get identity details
        identity = Identity.get_by_id(identity_id)
        if not identity:
            flash('Identity not found.', 'error')
            return redirect(url_for('auth.logout'))

        # Get user's Selas with mining information
        user_selas = db.query("""
            SELECT *,
                   COALESCE(mining_tier, 'basic') as mining_tier,
                   COALESCE(mining_power, 1) as mining_power,
                   COALESCE(mining_rewards_earned, 0) as mining_rewards_earned,
                   COALESCE(blocks_mined, 0) as blocks_mined
            FROM selas
            WHERE identity_id = ?
            ORDER BY created_at DESC
        """, (identity_id,))

        # Get user's transactions
        user_transactions = db.query("""
            SELECT * FROM transactions
            WHERE sender = ?
            ORDER BY created_at DESC
            LIMIT 10
        """, (identity_id,))

        # Get token balances (if token_balances table exists)
        token_balances = []
        if db.table_exists('token_balances'):
            token_balances = db.query("""
                SELECT tb.*, t.name, t.symbol, t.category
                FROM token_balances tb
                JOIN tokens t ON tb.token_id = t.token_id
                WHERE tb.identity_id = ?
                ORDER BY tb.balance DESC
            """, (identity_id,))

        # Calculate basic stats including mining information
        stats = {
            'selas_owned': len(user_selas),
            'transactions_sent': len(user_transactions),
            'total_token_balance': sum(tb.get('balance', 0) for tb in token_balances),
            'active_selas': len([s for s in user_selas if s['status'] == 'active']),
            'total_mining_rewards': sum(s.get('mining_rewards_earned', 0) for s in user_selas),
            'total_blocks_mined': sum(s.get('blocks_mined', 0) for s in user_selas),
            'highest_mining_tier': max([s.get('mining_tier', 'basic') for s in user_selas] + ['basic']),
            'total_mining_power': sum(s.get('mining_power', 1) for s in user_selas if s['status'] == 'active')
        }

        # Get network statistics for the dashboard
        latest_block = db.query_one("SELECT COUNT(*) as count FROM blocks")['count'] if db.table_exists('blocks') else 0
        total_validators = db.query_one("SELECT COUNT(*) as count FROM selas WHERE status = 'active'")['count']
        network_hashrate = "Auto"

        # Get trust score (placeholder for now)
        user_trust_score = "N/A"

        return render_template('dashboard/overview.html',
                             identity=identity,
                             user_selas=user_selas,
                             user_transactions=user_transactions,
                             token_balances=token_balances,
                             stats=stats,
                             latest_block=latest_block,
                             total_validators=total_validators,
                             network_hashrate=network_hashrate,
                             user_trust_score=user_trust_score,
                             user_tokens=stats['total_token_balance'])

    except Exception as e:
        logger.error(f"Error loading dashboard: {e}")
        flash('Error loading dashboard.', 'error')
        return redirect(url_for('index'))

@dashboard_bp.route('/identity')
@require_auth
def identity_details():
    """Identity management page."""
    try:
        identity_id = session['identity_id']
        identity = Identity.get_by_id(identity_id)

        if not identity:
            flash('Identity not found.', 'error')
            return redirect(url_for('auth.logout'))

        return render_template('dashboard/identity.html', identity=identity)

    except Exception as e:
        logger.error(f"Error loading identity details: {e}")
        flash('Error loading identity details.', 'error')
        return redirect(url_for('dashboard.overview'))

@dashboard_bp.route('/selas')
@require_auth
def selas():
    """Sela management page."""
    try:
        identity_id = session['identity_id']

        # Get user's Selas with detailed information
        user_selas = db.query("""
            SELECT * FROM selas
            WHERE identity_id = ?
            ORDER BY created_at DESC
        """, (identity_id,))

        # Parse metadata for each Sela
        for sela in user_selas:
            try:
                sela['metadata_parsed'] = json.loads(sela['metadata'])
            except:
                sela['metadata_parsed'] = {}

        return render_template('dashboard/selas.html', user_selas=user_selas)

    except Exception as e:
        logger.error(f"Error loading Selas: {e}")
        flash('Error loading Selas.', 'error')
        return redirect(url_for('dashboard.overview'))

@dashboard_bp.route('/sela/<sela_id>/configure')
@require_auth
def configure_sela(sela_id):
    """Configure Sela for mining."""
    try:
        identity_id = session['identity_id']

        # Verify ownership
        sela = db.query_one("""
            SELECT * FROM selas
            WHERE sela_id = ? AND identity_id = ?
        """, (sela_id, identity_id))

        if not sela:
            flash('Sela not found or access denied.', 'error')
            return redirect(url_for('dashboard.selas'))

        return render_template('dashboard/configure_sela.html', sela=sela)

    except Exception as e:
        logger.error(f"Error loading Sela configuration: {e}")
        flash('Error loading Sela configuration.', 'error')
        return redirect(url_for('dashboard.selas'))

@dashboard_bp.route('/sela/<sela_id>/configure', methods=['POST'])
@require_auth
def configure_sela_post(sela_id):
    """Handle Sela configuration."""
    try:
        identity_id = session['identity_id']

        # Verify ownership
        sela = db.query_one("""
            SELECT * FROM selas
            WHERE sela_id = ? AND identity_id = ?
        """, (sela_id, identity_id))

        if not sela:
            flash('Sela not found or access denied.', 'error')
            return redirect(url_for('dashboard.selas'))

        # Get configuration data
        private_key = request.form.get('private_key', '').strip()
        auto_mine = request.form.get('auto_mine') == 'on'
        mine_interval = int(request.form.get('mine_interval', 60))

        if not private_key:
            flash('Private key is required for mining configuration.', 'error')
            return render_template('dashboard/configure_sela.html', sela=sela)

        # Create mining configuration
        from scripts.configure_sela_miner import create_sela_config, create_activity_ledger

        config = create_sela_config(sela_id, identity_id, private_key)
        create_activity_ledger(sela_id)

        flash(f'Sela "{sela["name"]}" configured for mining successfully!', 'success')
        return redirect(url_for('dashboard.selas'))

    except Exception as e:
        logger.error(f"Error configuring Sela: {e}")
        flash('Sela configuration failed. Please try again.', 'error')
        return redirect(url_for('dashboard.configure_sela', sela_id=sela_id))

@dashboard_bp.route('/transactions')
@require_auth
def transactions():
    """Transaction history page."""
    try:
        identity_id = session['identity_id']

        # Get user's transactions with pagination
        page = request.args.get('page', 1, type=int)
        per_page = 20
        offset = (page - 1) * per_page

        user_transactions = db.query("""
            SELECT * FROM transactions
            WHERE sender = ?
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?
        """, (identity_id, per_page, offset))

        # Get total count for pagination
        total_count = db.query_one("""
            SELECT COUNT(*) as count FROM transactions
            WHERE sender = ?
        """, (identity_id,))['count']

        # Parse transaction data
        for tx in user_transactions:
            try:
                tx['data_parsed'] = json.loads(tx['data'])
            except:
                tx['data_parsed'] = {}

        # Calculate pagination
        total_pages = (total_count + per_page - 1) // per_page

        return render_template('dashboard/transactions.html',
                             transactions=user_transactions,
                             page=page,
                             total_pages=total_pages,
                             total_count=total_count)

    except Exception as e:
        logger.error(f"Error loading transactions: {e}")
        flash('Error loading transactions.', 'error')
        return redirect(url_for('dashboard.overview'))

@dashboard_bp.route('/profile')
@require_auth
def profile():
    """User profile management page."""
    try:
        identity_id = session['identity_id']

        # Get identity details
        identity = db.query_one("SELECT * FROM identities WHERE identity_id = ?", (identity_id,))
        if not identity:
            flash('Identity not found.', 'error')
            return redirect(url_for('auth.logout'))

        # Parse metadata
        try:
            identity['metadata_parsed'] = json.loads(identity['metadata'])
        except:
            identity['metadata_parsed'] = {}

        # Get user's Sela if they have one
        user_sela = db.query_one("SELECT * FROM selas WHERE identity_id = ?", (identity_id,))
        if user_sela:
            try:
                user_sela['metadata_parsed'] = json.loads(user_sela['metadata'])
            except:
                user_sela['metadata_parsed'] = {}

        return render_template('dashboard/profile.html',
                             identity=identity,
                             user_sela=user_sela)

    except Exception as e:
        logger.error(f"Error loading profile: {e}")
        flash('Error loading profile.', 'error')
        return redirect(url_for('dashboard.overview'))

@dashboard_bp.route('/profile/update', methods=['POST'])
@require_auth
def update_profile():
    """Update user profile information."""
    try:
        identity_id = session['identity_id']

        # Get current identity
        identity = db.query_one("SELECT * FROM identities WHERE identity_id = ?", (identity_id,))
        if not identity:
            flash('Identity not found.', 'error')
            return redirect(url_for('dashboard.profile'))

        # Parse current metadata
        try:
            current_metadata = json.loads(identity['metadata'])
        except:
            current_metadata = {}

        # Get form data
        name = request.form.get('name', '').strip()
        email = request.form.get('email', '').strip()

        # Validate required fields
        if not name:
            flash('Name is required.', 'error')
            return redirect(url_for('dashboard.profile'))

        if not email:
            flash('Email is required.', 'error')
            return redirect(url_for('dashboard.profile'))

        # Validate email format
        import re
        email_pattern = r'^[^\s@]+@[^\s@]+\.[^\s@]+$'
        if not re.match(email_pattern, email):
            flash('Please enter a valid email address.', 'error')
            return redirect(url_for('dashboard.profile'))

        # Check if email is already taken by another user
        existing_email = db.query_one("""
            SELECT identity_id FROM identities
            WHERE email = ? AND identity_id != ?
        """, (email, identity_id))

        if existing_email:
            flash('This email address is already in use.', 'error')
            return redirect(url_for('dashboard.profile'))

        # Update identity information
        update_data = {
            'name': name,
            'email': email,
            'updated_at': int(time.time())
        }

        db.update('identities', update_data, 'identity_id = ?', (identity_id,))

        # Update session name if changed
        session['identity_name'] = name

        # Create audit transaction
        tx_data = {
            'op': 'UPDATE_PROFILE',
            'identity_id': identity_id,
            'changes': {
                'name': name,
                'email': email
            }
        }

        transaction_data = {
            'tx_id': str(uuid.uuid4()),
            'block_hash': None,
            'timestamp': int(time.time()),
            'op': 'UPDATE_PROFILE',
            'data': json.dumps(tx_data),
            'sender': identity_id,
            'signature': f'profile_update_{int(time.time())}',
            'status': 'confirmed',
            'created_at': int(time.time())
        }

        db.insert('transactions', transaction_data)

        flash('Profile updated successfully!', 'success')
        return redirect(url_for('dashboard.profile'))

    except Exception as e:
        logger.error(f"Error updating profile: {e}")
        flash('Error updating profile. Please try again.', 'error')
        return redirect(url_for('dashboard.profile'))

@dashboard_bp.route('/profile/business/update', methods=['POST'])
@require_auth
def update_business_profile():
    """Update business profile information for Sela validators."""
    try:
        identity_id = session['identity_id']

        # Get user's Sela
        user_sela = db.query_one("SELECT * FROM selas WHERE identity_id = ?", (identity_id,))
        if not user_sela:
            flash('You do not have a registered business validator.', 'error')
            return redirect(url_for('dashboard.profile'))

        # Get form data
        business_name = request.form.get('business_name', '').strip()
        description = request.form.get('description', '').strip()
        address = request.form.get('address', '').strip()
        phone = request.form.get('phone', '').strip()
        website = request.form.get('website', '').strip()
        services = request.form.get('services', '').strip()
        category = request.form.get('category', '').strip()

        # Validate required fields
        if not business_name:
            flash('Business name is required.', 'error')
            return redirect(url_for('dashboard.profile'))

        if not category:
            flash('Business category is required.', 'error')
            return redirect(url_for('dashboard.profile'))

        # Validate website URL if provided
        if website and not website.startswith(('http://', 'https://')):
            website = 'https://' + website

        # Validate phone format (basic validation)
        if phone:
            import re
            phone_pattern = r'^[\+]?[1-9][\d]{0,15}$|^[\(\)\d\s\-\+\.]{10,}$'
            if not re.match(phone_pattern, phone.replace(' ', '').replace('-', '').replace('(', '').replace(')', '')):
                flash('Please enter a valid phone number.', 'error')
                return redirect(url_for('dashboard.profile'))

        # Update business information
        update_data = {
            'name': business_name,
            'description': description,
            'address': address,
            'phone': phone,
            'website': website,
            'services': services,
            'category': category,
            'updated_at': int(time.time())
        }

        db.update('selas', update_data, 'sela_id = ?', (user_sela['sela_id'],))

        # Create audit transaction
        tx_data = {
            'op': 'UPDATE_BUSINESS_PROFILE',
            'identity_id': identity_id,
            'sela_id': user_sela['sela_id'],
            'changes': {
                'business_name': business_name,
                'description': description,
                'address': address,
                'phone': phone,
                'website': website,
                'services': services,
                'category': category
            }
        }

        transaction_data = {
            'tx_id': str(uuid.uuid4()),
            'block_hash': None,
            'timestamp': int(time.time()),
            'op': 'UPDATE_BUSINESS_PROFILE',
            'data': json.dumps(tx_data),
            'sender': identity_id,
            'signature': f'business_update_{int(time.time())}',
            'status': 'confirmed',
            'created_at': int(time.time())
        }

        db.insert('transactions', transaction_data)

        flash('Business profile updated successfully!', 'success')
        return redirect(url_for('dashboard.profile'))

    except Exception as e:
        logger.error(f"Error updating business profile: {e}")
        flash('Error updating business profile. Please try again.', 'error')
        return redirect(url_for('dashboard.profile'))
