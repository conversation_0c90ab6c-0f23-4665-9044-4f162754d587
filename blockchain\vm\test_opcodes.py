"""
Test script for Onnyx VM opcodes.

This script tests the opcode validation functions defined in opcodes.py.
"""

import sys
import os

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from blockchain.vm.opcodes import (
    op_mint, op_send, op_identity, op_scroll,
    OpcodeMintError, OpcodeSendError, OpcodeIdentityError, OpcodeScrollError,
    identity, selas, tokens, ledger
)

def test_op_mint():
    """Test the OP_MINT opcode."""
    print("\nTesting OP_MINT...")
    
    # Setup test data
    identity.identities["id1"] = {"name": "Test Identity 1"}
    selas.selas["id1"] = {"name": "Test Sela 1"}
    
    # Test valid mint transaction
    valid_tx = {
        "from": "id1",
        "data": {
            "symbol": "TKN",
            "supply": 1000
        }
    }
    
    try:
        result = op_mint(valid_tx)
        print(f"Valid mint transaction: {result}")
    except OpcodeMintError as e:
        print(f"Error: {e}")
    
    # Test invalid mint transaction (exceeds cap)
    invalid_tx = {
        "from": "id1",
        "data": {
            "symbol": "TKN2",
            "supply": 2000  # Exceeds the default cap of 1000
        }
    }
    
    try:
        result = op_mint(invalid_tx)
        print(f"Invalid mint transaction (should not reach here): {result}")
    except OpcodeMintError as e:
        print(f"Expected error: {e}")
    
    # Test invalid mint transaction (not a Sela)
    identity.identities["id2"] = {"name": "Test Identity 2"}
    invalid_tx2 = {
        "from": "id2",  # Not in selas.selas
        "data": {
            "symbol": "TKN3",
            "supply": 500
        }
    }
    
    try:
        result = op_mint(invalid_tx2)
        print(f"Invalid mint transaction (should not reach here): {result}")
    except OpcodeMintError as e:
        print(f"Expected error: {e}")

def test_op_send():
    """Test the OP_SEND opcode."""
    print("\nTesting OP_SEND...")
    
    # Setup test data
    identity.identities["id1"] = {"name": "Test Identity 1"}
    identity.identities["id2"] = {"name": "Test Identity 2"}
    tokens.tokens["TKN"] = {"symbol": "TKN", "supply": 1000}
    ledger.balances["id1:TKN"] = 500
    
    # Test valid send transaction
    valid_tx = {
        "from": "id1",
        "data": {
            "token_id": "TKN",
            "to": "id2",
            "amount": 100
        }
    }
    
    try:
        result = op_send(valid_tx)
        print(f"Valid send transaction: {result}")
    except OpcodeSendError as e:
        print(f"Error: {e}")
    
    # Test invalid send transaction (insufficient balance)
    invalid_tx = {
        "from": "id1",
        "data": {
            "token_id": "TKN",
            "to": "id2",
            "amount": 1000  # Exceeds balance of 500
        }
    }
    
    try:
        result = op_send(invalid_tx)
        print(f"Invalid send transaction (should not reach here): {result}")
    except OpcodeSendError as e:
        print(f"Expected error: {e}")
    
    # Test invalid send transaction (invalid recipient)
    invalid_tx2 = {
        "from": "id1",
        "data": {
            "token_id": "TKN",
            "to": "id3",  # Does not exist
            "amount": 100
        }
    }
    
    try:
        result = op_send(invalid_tx2)
        print(f"Invalid send transaction (should not reach here): {result}")
    except OpcodeSendError as e:
        print(f"Expected error: {e}")

def test_op_identity():
    """Test the OP_IDENTITY opcode."""
    print("\nTesting OP_IDENTITY...")
    
    # Test valid identity transaction
    valid_tx = {
        "data": {
            "identity_id": "id3",
            "name": "Test Identity 3",
            "public_key": "0x123456789abcdef"
        }
    }
    
    try:
        result = op_identity(valid_tx)
        print(f"Valid identity transaction: {result}")
    except OpcodeIdentityError as e:
        print(f"Error: {e}")
    
    # Test invalid identity transaction (already exists)
    identity.identities["id4"] = {"name": "Test Identity 4"}
    invalid_tx = {
        "data": {
            "identity_id": "id4",  # Already exists
            "name": "Test Identity 4 Duplicate",
            "public_key": "0x123456789abcdef"
        }
    }
    
    try:
        result = op_identity(invalid_tx)
        print(f"Invalid identity transaction (should not reach here): {result}")
    except OpcodeIdentityError as e:
        print(f"Expected error: {e}")

def test_op_scroll():
    """Test the OP_SCROLL opcode."""
    print("\nTesting OP_SCROLL...")
    
    # Setup test data
    identity.identities["id1"] = {"name": "Test Identity 1"}
    identity.badges["id1"] = ["CREATOR"]
    
    # Test valid scroll transaction
    valid_tx = {
        "from": "id1",
        "data": {
            "title": "Test Scroll",
            "description": "This is a test scroll",
            "category": "test"
        }
    }
    
    try:
        result = op_scroll(valid_tx)
        print(f"Valid scroll transaction: {result}")
    except OpcodeScrollError as e:
        print(f"Error: {e}")
    
    # Test invalid scroll transaction (no CREATOR badge)
    identity.identities["id2"] = {"name": "Test Identity 2"}
    identity.badges["id2"] = []  # No badges
    invalid_tx = {
        "from": "id2",
        "data": {
            "title": "Test Scroll",
            "description": "This is a test scroll",
            "category": "test"
        }
    }
    
    try:
        result = op_scroll(invalid_tx)
        print(f"Invalid scroll transaction (should not reach here): {result}")
    except OpcodeScrollError as e:
        print(f"Expected error: {e}")

def main():
    """Run all tests."""
    print("Testing Onnyx VM Opcodes")
    print("========================")
    
    test_op_mint()
    test_op_send()
    test_op_identity()
    test_op_scroll()
    
    print("\nAll tests completed.")

if __name__ == "__main__":
    main()
