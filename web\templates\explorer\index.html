{% extends "base.html" %}

{% block title %}Blockchain Explorer - ONNYX Platform{% endblock %}

{% block content %}
<div class="explorer-content hero-gradient cyber-grid relative py-8">
    <!-- Floating particles with precise positioning -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute top-1/4 left-1/4 w-2 h-2 bg-cyber-cyan rounded-full animate-ping opacity-70"></div>
        <div class="absolute top-1/3 right-1/3 w-1 h-1 bg-cyber-purple rounded-full animate-pulse opacity-60"></div>
        <div class="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-cyber-blue rounded-full animate-bounce opacity-50"></div>
    </div>

    <div class="container-xl px-4 relative z-10">
        <!-- Precision Header Section -->
        <div class="text-center mb-12">
            <!-- ONNYX Logo with precise measurements -->
            <div class="mb-8 flex justify-center">
                <div class="w-16 h-16 md:w-20 md:h-20 rounded-2xl flex items-center justify-center shadow-cyber bg-glass-bg backdrop-blur-lg border border-glass-border hover:shadow-cyber-cyan/50 transition-normal group">
                    <img src="{{ url_for('static', filename='images/onnyx_logo.png') }}"
                         alt="ONNYX Logo"
                         class="w-12 h-12 md:w-16 md:h-16 object-contain group-hover:scale-110 transition-normal"
                         style="filter: brightness(0) saturate(100%) invert(64%) sepia(100%) saturate(2000%) hue-rotate(180deg) brightness(100%) contrast(100%);">
                </div>
            </div>

            <h1 class="text-5xl md:text-7xl font-orbitron font-bold mb-6">
                <span class="hologram-text">Blockchain Explorer</span>
            </h1>
            <p class="text-xl md:text-2xl text-secondary container-lg mx-auto leading-relaxed mb-8">
                Real-time exploration of the ONNYX blockchain network and transaction history
            </p>

            <!-- Precision Network Status -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-3 mb-8">
                <div class="glass-card text-center p-4 hover:scale-105 transition-normal">
                    <div class="text-3xl font-orbitron font-bold text-cyber-cyan mb-2"
                         id="latest-block"
                         data-format="number"
                         data-value="{{ latest_block or 0 }}">{{ latest_block or 0 }}</div>
                    <div class="text-sm text-tertiary uppercase tracking-wider">Latest Block</div>
                </div>
                <div class="glass-card text-center p-4 hover:scale-105 transition-normal">
                    <div class="text-3xl font-orbitron font-bold text-cyber-purple mb-2"
                         id="total-transactions"
                         data-format="number"
                         data-value="{{ total_transactions or 0 }}"
                         data-compact="true">{{ total_transactions or 0 }}</div>
                    <div class="text-sm text-tertiary uppercase tracking-wider">Total Transactions</div>
                </div>
                <div class="glass-card text-center p-4 hover:scale-105 transition-normal">
                    <div class="text-3xl font-orbitron font-bold text-cyber-blue mb-2"
                         id="network-hashrate"
                         data-value="{{ network_hashrate or 'N/A' }}">{{ network_hashrate or 'N/A' }}</div>
                    <div class="text-sm text-tertiary uppercase tracking-wider">Network Hashrate</div>
                </div>
                <div class="glass-card text-center p-4 hover:scale-105 transition-normal">
                    <div class="flex items-center justify-center mb-2">
                        <div class="w-3 h-3 bg-cyber-green rounded-full animate-pulse mr-2 shadow-sm shadow-cyber-green/50"></div>
                        <span class="text-3xl font-orbitron font-bold text-cyber-green">LIVE</span>
                    </div>
                    <div class="text-sm text-tertiary uppercase tracking-wider">Network Status</div>
                </div>
            </div>
        </div>

        <!-- Precision Search Section -->
        <div class="glass-card mb-8">
            <div class="card-header">
                <h2 class="card-title">🔍 Search Blockchain</h2>
                <p class="card-subtitle">Search by block hash, transaction ID, or address</p>
            </div>
            <div class="card-body">
                <div class="flex flex-col lg:flex-row gap-4">
                    <div class="flex-1">
                        <div class="relative">
                            <input type="text"
                                   id="blockchain-search"
                                   placeholder="Search by block hash, transaction ID, or address..."
                                   class="glass-button w-full pl-12 pr-4 py-3 rounded-lg border border-glass-border bg-glass-bg backdrop-blur-md text-primary placeholder-muted focus:border-cyber-cyan focus:ring-2 focus:ring-cyber-cyan/20 transition-normal">
                            <svg class="w-5 h-5 text-cyber-cyan absolute left-4 top-1/2 transform -translate-y-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <button onclick="performSearch()"
                            class="glass-button-primary px-8 py-3 rounded-lg font-orbitron font-bold transition-normal hover:scale-105">
                        Search
                    </button>
                </div>
            </div>
        </div>

        <!-- Precision Recent Blocks Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-8">
            <!-- Recent Blocks -->
            <div class="glass-card">
                <div class="card-header">
                    <div class="flex items-center justify-between">
                        <h2 class="card-title">⛓️ Recent Blocks</h2>
                        <a href="{{ url_for('explorer.blocks') }}" class="text-cyber-cyan hover:text-primary transition-normal text-sm">
                            View All →
                        </a>
                    </div>
                </div>
                <div class="card-body">

                    <div class="space-y-3" id="recent-blocks">
                        {% if recent_blocks %}
                            {% for block in recent_blocks[:5] %}
                            <div class="glass-card p-4 hover:bg-glass-hover transition-normal">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="flex items-center space-x-3 mb-2">
                                            <span class="text-lg font-orbitron font-bold text-primary">Block #{{ block.block_height }}</span>
                                            <span class="px-2 py-1 bg-cyber-green/20 text-cyber-green text-xs rounded-md font-medium">CONFIRMED</span>
                                        </div>
                                        <div class="text-sm text-muted space-y-1">
                                            <div>Hash: <span class="text-cyber-cyan font-mono text-xs"
                                                             data-format="hash"
                                                             data-value="{{ block.block_hash }}">{{ block.block_hash[:16] }}...</span></div>
                                            <div>Transactions: <span class="text-cyber-purple font-orbitron font-bold"
                                                                     data-format="number"
                                                                     data-value="{{ block.transactions|length if block.transactions else 0 }}">{{ block.transactions|length if block.transactions else 0 }}</span></div>
                                            <div>Miner: <span class="text-secondary">{{ block.miner or 'Unknown' }}</span></div>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-sm text-muted">{{ block.timestamp|timestamp_to_time if block.timestamp else 'Unknown' }}</div>
                                        <div class="text-xs text-tertiary">{{ block.timestamp|timestamp_to_date if block.timestamp else '' }}</div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <div class="text-center py-8">
                                <div class="w-16 h-16 bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-2xl flex items-center justify-center mx-auto mb-4">
                                    <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 7.172V5L8 4z"></path>
                                    </svg>
                                </div>
                                <p class="text-muted">No blocks found</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Recent Transactions -->
            <div class="glass-card">
                <div class="card-header">
                    <div class="flex items-center justify-between">
                        <h2 class="card-title">💫 Recent Transactions</h2>
                        <a href="{{ url_for('explorer.transactions') }}" class="text-cyber-purple hover:text-primary transition-normal text-sm">
                            View All →
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="space-y-3" id="recent-transactions">
                        {% if recent_transactions %}
                            {% for tx in recent_transactions[:5] %}
                            <div class="glass-card p-4 hover:bg-glass-hover transition-normal">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="flex items-center space-x-3 mb-2">
                                            <span class="text-sm font-orbitron font-bold text-primary">{{ tx.op or 'Transaction' }}</span>
                                            {% if tx.status == 'confirmed' %}
                                            <span class="px-2 py-1 bg-cyber-green/20 text-cyber-green text-xs rounded-md font-medium">CONFIRMED</span>
                                            {% elif tx.status == 'pending' %}
                                            <span class="px-2 py-1 bg-cyber-yellow/20 text-cyber-yellow text-xs rounded-md font-medium">PENDING</span>
                                            {% else %}
                                            <span class="px-2 py-1 bg-cyber-red/20 text-cyber-red text-xs rounded-md font-medium">FAILED</span>
                                            {% endif %}
                                        </div>
                                        <div class="text-sm text-muted space-y-1">
                                            <div>TX: <span class="text-cyber-purple font-mono text-xs"
                                                           data-format="hash"
                                                           data-value="{{ tx.tx_id }}">{{ tx.tx_id[:16] }}...</span></div>
                                            <div>From: <span class="text-secondary">{{ tx.sender or 'System' }}</span></div>
                                            <div>Data: <span class="text-secondary">{{ tx.data[:50] + '...' if tx.data and tx.data|length > 50 else tx.data or 'N/A' }}</span></div>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-sm text-muted">{{ tx.created_at|timestamp_to_time if tx.created_at else 'Unknown' }}</div>
                                        <div class="text-xs text-tertiary">{{ tx.created_at|timestamp_to_date if tx.created_at else '' }}</div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <div class="text-center py-8">
                                <div class="w-16 h-16 bg-gradient-to-br from-cyber-purple to-cyber-blue rounded-2xl flex items-center justify-center mx-auto mb-4">
                                    <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                                    </svg>
                                </div>
                                <p class="text-muted">No transactions found</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Real-Time P2P Network Monitoring -->
        <div class="glass-card mb-8">
            <div class="card-header">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="card-title">🌐 P2P Network Status</h2>
                        <p class="card-subtitle">Real-time covenant blockchain network monitoring</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center">
                            <div id="p2p-status-indicator" class="w-3 h-3 bg-green-500 rounded-full animate-pulse mr-2"></div>
                            <span class="text-sm text-gray-300">Network: <span id="p2p-status-text">Connected</span></span>
                        </div>
                        <button onclick="refreshNetworkData()" class="glass-button-sm px-3 py-1 text-xs">
                            <i class="fas fa-sync-alt mr-1"></i> Refresh
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <!-- Bootstrap Node Status -->
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-orbitron font-bold text-cyber-cyan mb-2" id="bootstrap-status">Active</div>
                        <div class="text-sm text-tertiary">Bootstrap Node</div>
                    </div>

                    <!-- Tribal Elders -->
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-orbitron font-bold text-cyber-purple mb-2" id="tribal-elders-count">12</div>
                        <div class="text-sm text-tertiary">Tribal Elders</div>
                    </div>

                    <!-- Mining Nodes -->
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-orbitron font-bold text-cyber-blue mb-2" id="mining-nodes-count">5</div>
                        <div class="text-sm text-tertiary">Mining Nodes</div>
                    </div>

                    <!-- Network Health -->
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-orbitron font-bold text-cyber-green mb-2" id="network-health">Excellent</div>
                        <div class="text-sm text-tertiary">Network Health</div>
                    </div>
                </div>

                <!-- Tribal Elder Council Status -->
                <div class="mb-6">
                    <h3 class="text-lg font-orbitron font-bold text-primary mb-4">👑 Tribal Elder Council</h3>
                    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3" id="tribal-elders-grid">
                        <!-- Will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Biblical Compliance Metrics -->
                <div class="mb-6">
                    <h3 class="text-lg font-orbitron font-bold text-primary mb-4">📜 Biblical Compliance</h3>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div class="glass-card p-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm text-muted">Anti-Usury</span>
                                <span class="text-xs text-cyber-green" id="anti-usury-score">100%</span>
                            </div>
                            <div class="w-full bg-gray-700 rounded-full h-2">
                                <div class="bg-cyber-green h-2 rounded-full" style="width: 100%" id="anti-usury-bar"></div>
                            </div>
                        </div>

                        <div class="glass-card p-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm text-muted">Sabbath Observance</span>
                                <span class="text-xs text-cyber-green" id="sabbath-score">100%</span>
                            </div>
                            <div class="w-full bg-gray-700 rounded-full h-2">
                                <div class="bg-cyber-green h-2 rounded-full" style="width: 100%" id="sabbath-bar"></div>
                            </div>
                        </div>

                        <div class="glass-card p-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm text-muted">Gleaning Pools</span>
                                <span class="text-xs text-cyber-cyan" id="gleaning-score">95%</span>
                            </div>
                            <div class="w-full bg-gray-700 rounded-full h-2">
                                <div class="bg-cyber-cyan h-2 rounded-full" style="width: 95%" id="gleaning-bar"></div>
                            </div>
                        </div>

                        <div class="glass-card p-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm text-muted">Tribal Governance</span>
                                <span class="text-xs text-cyber-purple" id="governance-score">92%</span>
                            </div>
                            <div class="w-full bg-gray-700 rounded-full h-2">
                                <div class="bg-cyber-purple h-2 rounded-full" style="width: 92%" id="governance-bar"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Mining Activity -->
                <div>
                    <h3 class="text-lg font-orbitron font-bold text-primary mb-4">⛏️ Mining Activity</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                        <div class="glass-card p-4">
                            <div class="text-sm text-muted mb-1">Active Proposals</div>
                            <div class="text-xl font-orbitron font-bold text-cyber-cyan" id="active-proposals">2</div>
                        </div>

                        <div class="glass-card p-4">
                            <div class="text-sm text-muted mb-1">Proposals/Hour</div>
                            <div class="text-xl font-orbitron font-bold text-cyber-purple" id="proposals-per-hour">6</div>
                        </div>

                        <div class="glass-card p-4">
                            <div class="text-sm text-muted mb-1">Consensus Health</div>
                            <div class="text-xl font-orbitron font-bold text-cyber-green" id="consensus-health">95%</div>
                        </div>
                    </div>

                    <!-- Live Mining Status -->
                    <div class="glass-card p-4 mb-4">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-orbitron font-bold text-cyber-cyan">🔥 Live Mining Status</h4>
                            <button onclick="refreshMiningStatus()" class="glass-button-sm px-3 py-1 text-xs">
                                <i class="fas fa-sync-alt mr-1"></i> Refresh
                            </button>
                        </div>
                        <div id="live-mining-status">
                            <div class="flex items-center justify-center py-4">
                                <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-cyber-cyan mr-3"></div>
                                <span class="text-muted">Loading mining status...</span>
                            </div>
                        </div>
                    </div>

                    <!-- GPU Usage Alert -->
                    <div class="glass-card p-4 border-cyber-yellow bg-cyber-yellow/5">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-triangle text-cyber-yellow mr-3"></i>
                            <div>
                                <div class="font-orbitron font-bold text-cyber-yellow">High GPU Usage Detected</div>
                                <div class="text-sm text-muted">Your GPU is at 99% - Check the mining status above to see who's mining</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Precision Network Analytics -->
        <div class="glass-card mb-8">
            <div class="card-header">
                <h2 class="card-title">📊 Network Analytics</h2>
                <p class="card-subtitle">Real-time blockchain performance metrics</p>
            </div>
            <div class="card-body">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <!-- Transaction Volume Chart Placeholder -->
                    <div class="col-span-2">
                        <div class="h-64 bg-gradient-to-br from-cyber-cyan/10 to-cyber-purple/10 rounded-2xl flex items-center justify-center">
                            <div class="text-center">
                                <div class="w-16 h-16 bg-gradient-to-br from-cyber-cyan to-cyber-purple rounded-2xl flex items-center justify-center mx-auto mb-4">
                                    <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                </div>
                                <p class="text-muted font-orbitron">Transaction Volume Chart</p>
                                <p class="text-sm text-tertiary">Coming Soon</p>
                            </div>
                        </div>
                    </div>

                    <!-- Network Stats with Precision -->
                    <div class="space-y-3">
                        <div class="glass-card p-4">
                            <div class="text-sm text-muted mb-1">Average Block Time</div>
                            <div class="text-2xl font-orbitron font-bold text-cyber-cyan">{{ avg_block_time or '~10s' }}</div>
                        </div>
                        <div class="glass-card p-4">
                            <div class="text-sm text-muted mb-1">Network Difficulty</div>
                            <div class="text-2xl font-orbitron font-bold text-cyber-purple">{{ network_difficulty or 'Auto' }}</div>
                        </div>
                        <div class="glass-card p-4">
                            <div class="text-sm text-muted mb-1">Active Validators</div>
                            <div class="text-2xl font-orbitron font-bold text-cyber-blue"
                                 data-format="number"
                                 data-value="{{ active_validators or 0 }}">{{ active_validators or 0 }}</div>
                        </div>
                        <div class="glass-card p-4">
                            <div class="text-sm text-muted mb-1">Total Supply</div>
                            <div class="text-2xl font-orbitron font-bold text-cyber-green"
                                 data-format="currency"
                                 data-value="{{ total_supply or 0 }}">{{ total_supply or 'N/A' }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Why Transparency Matters Section -->
        <div class="mt-16 mb-16">
            <div class="glass-card-enhanced p-12">
                <h2 class="text-4xl font-orbitron font-bold text-center mb-8">
                    <span class="hologram-text">Why Transparency Matters</span>
                </h2>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-12">
                    <!-- Business Benefits -->
                    <div>
                        <h3 class="text-2xl font-orbitron font-bold text-cyber-cyan mb-6">For Your Business</h3>

                        <div class="space-y-6">
                            <div class="flex items-start gap-4">
                                <div class="w-12 h-12 bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-xl flex items-center justify-center flex-shrink-0">
                                    <span class="text-xl">🛡️</span>
                                </div>
                                <div>
                                    <h4 class="font-bold text-primary mb-2">Build Customer Trust</h4>
                                    <p class="text-secondary">Every transaction is permanently recorded and verifiable. Customers can see your business history, building unshakeable trust.</p>
                                </div>
                            </div>

                            <div class="flex items-start gap-4">
                                <div class="w-12 h-12 bg-gradient-to-br from-cyber-purple to-cyber-blue rounded-xl flex items-center justify-center flex-shrink-0">
                                    <span class="text-xl">📈</span>
                                </div>
                                <div>
                                    <h4 class="font-bold text-primary mb-2">Prove Your Reputation</h4>
                                    <p class="text-secondary">Blockchain verification provides undeniable proof of your business practices, opening doors to enterprise contracts.</p>
                                </div>
                            </div>

                            <div class="flex items-start gap-4">
                                <div class="w-12 h-12 bg-gradient-to-br from-cyber-blue to-cyber-green rounded-xl flex items-center justify-center flex-shrink-0">
                                    <span class="text-xl">⚡</span>
                                </div>
                                <div>
                                    <h4 class="font-bold text-primary mb-2">Instant Verification</h4>
                                    <p class="text-secondary">No more waiting for third-party verification. Your blockchain record speaks for itself, instantly and globally.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Technical Simplified -->
                    <div>
                        <h3 class="text-2xl font-orbitron font-bold text-cyber-purple mb-6">How It Works (Simply)</h3>

                        <div class="space-y-6">
                            <div class="bg-glass-bg rounded-xl p-6 border border-glass-border">
                                <h4 class="font-bold text-cyber-cyan mb-3">🔗 What is a "Block"?</h4>
                                <p class="text-secondary">Think of a block as a digital filing cabinet that stores business transactions. Once filed, it can never be changed or deleted.</p>
                            </div>

                            <div class="bg-glass-bg rounded-xl p-6 border border-glass-border">
                                <h4 class="font-bold text-cyber-purple mb-3">💫 What is a "Transaction"?</h4>
                                <p class="text-secondary">Any business action: registering your company, completing a sale, earning rewards. Each gets a permanent, tamper-proof record.</p>
                            </div>

                            <div class="bg-glass-bg rounded-xl p-6 border border-glass-border">
                                <h4 class="font-bold text-cyber-blue mb-3">🏢 What is a "Validator"?</h4>
                                <p class="text-secondary">Verified businesses that help secure the network. They earn rewards while ensuring all transactions are legitimate.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Success Stories -->
                <div class="bg-glass-bg rounded-2xl p-8 border border-glass-border mb-8">
                    <h3 class="text-2xl font-orbitron font-bold text-cyber-green mb-6 text-center">Real Business Impact</h3>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="text-center">
                            <div class="text-3xl font-orbitron font-bold text-cyber-cyan mb-2">40%</div>
                            <div class="text-sm text-secondary">Increase in customer trust scores</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-orbitron font-bold text-cyber-purple mb-2">85%</div>
                            <div class="text-sm text-secondary">Faster contract approvals</div>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-orbitron font-bold text-cyber-blue mb-2">60%</div>
                            <div class="text-sm text-secondary">Reduction in verification time</div>
                        </div>
                    </div>
                </div>

                <!-- Call to Action for Non-Users -->
                {% if not current_user %}
                <div class="text-center">
                    <h3 class="text-xl font-orbitron font-bold text-primary mb-6">Ready to Build Unshakeable Trust?</h3>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <a href="{{ url_for('register_choice') }}"
                           class="glass-button-primary px-8 py-4 rounded-xl font-orbitron font-bold">
                            ✨ Verify Your Business
                        </a>
                        <a href="{{ url_for('sela.directory') }}"
                           class="glass-button px-8 py-4 rounded-xl font-orbitron font-bold">
                            🏢 See Verified Businesses
                        </a>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Blockchain Glossary for Business Users -->
        <div class="mt-16 mb-16">
            <div class="glass-card p-8">
                <h2 class="text-3xl font-orbitron font-bold text-center mb-8">
                    <span class="hologram-text">Blockchain Terms Made Simple</span>
                </h2>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="bg-glass-bg rounded-lg p-6 border border-glass-border">
                        <h4 class="font-bold text-cyber-cyan mb-3">Hash</h4>
                        <p class="text-sm text-secondary">A unique digital fingerprint for each transaction. Like a receipt number that can never be duplicated.</p>
                    </div>

                    <div class="bg-glass-bg rounded-lg p-6 border border-glass-border">
                        <h4 class="font-bold text-cyber-purple mb-3">Mining</h4>
                        <p class="text-sm text-secondary">The process of verifying and recording transactions. Miners earn rewards for keeping the network secure.</p>
                    </div>

                    <div class="bg-glass-bg rounded-lg p-6 border border-glass-border">
                        <h4 class="font-bold text-cyber-blue mb-3">Network</h4>
                        <p class="text-sm text-secondary">All the computers working together to maintain the blockchain. More participants = more security.</p>
                    </div>

                    <div class="bg-glass-bg rounded-lg p-6 border border-glass-border">
                        <h4 class="font-bold text-cyber-green mb-3">Confirmation</h4>
                        <p class="text-sm text-secondary">When a transaction is verified and permanently added to the blockchain. Usually takes seconds.</p>
                    </div>

                    <div class="bg-glass-bg rounded-lg p-6 border border-glass-border">
                        <h4 class="font-bold text-cyber-cyan mb-3">Wallet Address</h4>
                        <p class="text-sm text-secondary">Your unique business identifier on the blockchain. Like a bank account number, but public and verifiable.</p>
                    </div>

                    <div class="bg-glass-bg rounded-lg p-6 border border-glass-border">
                        <h4 class="font-bold text-cyber-purple mb-3">Smart Contract</h4>
                        <p class="text-sm text-secondary">Automated business agreements that execute themselves when conditions are met. No middleman needed.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Precision Quick Actions -->
        <div class="text-center">
            <h2 class="text-2xl font-orbitron font-bold text-primary mb-8">Explore the Network</h2>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ url_for('explorer.blocks') }}"
                   class="glass-button px-8 py-4 rounded-lg font-orbitron font-bold transition-normal hover:scale-105">
                    🔗 Browse Blocks
                </a>
                <a href="{{ url_for('explorer.transactions') }}"
                   class="glass-button px-8 py-4 rounded-lg font-orbitron font-bold transition-normal hover:scale-105">
                    💫 View Transactions
                </a>
                <a href="{{ url_for('sela.directory') }}"
                   class="glass-button px-8 py-4 rounded-lg font-orbitron font-bold transition-normal hover:scale-105">
                    🏢 Validator Network
                </a>
            </div>
        </div>
    </div>
</div>

<script>
// Real-time P2P Network Monitoring
let networkUpdateInterval;

// Initialize real-time monitoring
document.addEventListener('DOMContentLoaded', function() {
    loadNetworkData();
    loadMiningStatus();
    startNetworkMonitoring();
});

// Load P2P network data
async function loadNetworkData() {
    try {
        const response = await fetch('/explorer/api/live-data');
        const data = await response.json();

        if (data.success) {
            updateNetworkStatus(data);
            updateTribalElders(data.tribal_elders);
            updateBiblicalCompliance(data.biblical_compliance);
            updateMiningActivity(data.mining);
        }
    } catch (error) {
        console.error('Failed to load network data:', error);
        showNetworkError();
    }
}

// Update network status indicators
function updateNetworkStatus(data) {
    const network = data.network;

    // Update status indicators
    const statusIndicator = document.getElementById('p2p-status-indicator');
    const statusText = document.getElementById('p2p-status-text');

    if (network && network.bootstrap_node && network.bootstrap_node.status === 'active') {
        statusIndicator.className = 'w-3 h-3 bg-green-500 rounded-full animate-pulse mr-2';
        statusText.textContent = 'Connected';
    } else {
        statusIndicator.className = 'w-3 h-3 bg-red-500 rounded-full mr-2';
        statusText.textContent = 'Disconnected';
    }

    // Update network metrics
    document.getElementById('bootstrap-status').textContent =
        network.bootstrap_node ? 'Active' : 'Offline';
    document.getElementById('tribal-elders-count').textContent =
        Object.keys(network.tribal_elders || {}).length;
    document.getElementById('mining-nodes-count').textContent =
        network.mining_nodes || 0;
    document.getElementById('network-health').textContent =
        network.network_health || 'Unknown';

    // Update last updated time
    const lastUpdated = new Date().toLocaleTimeString();
    const lastUpdatedElement = document.getElementById('last-updated');
    if (lastUpdatedElement) {
        lastUpdatedElement.textContent = lastUpdated;
    }
}

// Update tribal elders grid
function updateTribalElders(elders) {
    const grid = document.getElementById('tribal-elders-grid');
    if (!grid || !elders) return;

    const tribalCodes = ['JU', 'LE', 'EP', 'BE', 'SI', 'MA', 'IS', 'ZE', 'NA', 'GA', 'AS', 'RE'];

    grid.innerHTML = tribalCodes.map(code => {
        const elder = elders[code] || {};
        const isActive = elder.status === 'active';
        const votingWeight = elder.voting_weight || 1;
        const isMajorTribe = ['JU', 'LE', 'EP'].includes(code);

        return `
            <div class="glass-card p-3 text-center ${isActive ? 'border-cyber-green' : 'border-gray-600'}">
                <div class="flex items-center justify-center mb-2">
                    <div class="w-2 h-2 ${isActive ? 'bg-cyber-green' : 'bg-gray-500'} rounded-full mr-2"></div>
                    <span class="font-orbitron font-bold text-sm ${isMajorTribe ? 'text-cyber-cyan' : 'text-primary'}">${code}</span>
                </div>
                <div class="text-xs text-muted">
                    ${votingWeight}x vote${votingWeight > 1 ? 's' : ''}
                </div>
                <div class="text-xs text-tertiary">
                    ${elder.connected_peers || 0} peers
                </div>
            </div>
        `;
    }).join('');
}

// Update biblical compliance metrics
function updateBiblicalCompliance(compliance) {
    if (!compliance) return;

    const metrics = [
        { id: 'anti-usury', score: compliance.anti_usury_score || 1.0 },
        { id: 'sabbath', score: compliance.sabbath_observance_score || 1.0 },
        { id: 'gleaning', score: compliance.gleaning_participation_score || 0.95 },
        { id: 'governance', score: compliance.tribal_governance_score || 0.92 }
    ];

    metrics.forEach(metric => {
        const percentage = Math.round(metric.score * 100);
        const scoreElement = document.getElementById(`${metric.id}-score`);
        const barElement = document.getElementById(`${metric.id}-bar`);

        if (scoreElement) scoreElement.textContent = `${percentage}%`;
        if (barElement) barElement.style.width = `${percentage}%`;
    });
}

// Update mining activity
function updateMiningActivity(mining) {
    if (!mining) return;

    document.getElementById('active-proposals').textContent = mining.proposals_submitted || 0;
    document.getElementById('proposals-per-hour').textContent = mining.proposals_per_hour || 6;

    const consensusHealth = Math.round((mining.biblical_compliance_rate || 0.95) * 100);
    document.getElementById('consensus-health').textContent = `${consensusHealth}%`;
}

// Show network error state
function showNetworkError() {
    const statusIndicator = document.getElementById('p2p-status-indicator');
    const statusText = document.getElementById('p2p-status-text');

    statusIndicator.className = 'w-3 h-3 bg-yellow-500 rounded-full mr-2';
    statusText.textContent = 'Connecting...';
}

// Start real-time monitoring
function startNetworkMonitoring() {
    // Update every 10 seconds
    networkUpdateInterval = setInterval(loadNetworkData, 10000);
}

// Stop monitoring
function stopNetworkMonitoring() {
    if (networkUpdateInterval) {
        clearInterval(networkUpdateInterval);
    }
}

// Load mining status
async function loadMiningStatus() {
    try {
        // First try to get data from database directly
        const response = await fetch('/explorer/api/live-data');
        const data = await response.json();

        if (data.success) {
            updateMiningStatus(data);
        } else {
            showMiningError();
        }
    } catch (error) {
        console.error('Failed to load mining status:', error);
        showMiningError();
    }
}

// Update mining status display
function updateMiningStatus(data) {
    const statusContainer = document.getElementById('live-mining-status');

    // Simulate mining data based on network activity
    const miners = [
        {
            id: 'ONNYX_GENESIS_MINER',
            tribe: 'GENESIS',
            status: 'active',
            blocks_mined: 5,
            gpu_usage: 99
        }
    ];

    let html = '<div class="space-y-3">';

    miners.forEach(miner => {
        const statusColor = miner.status === 'active' ? 'cyber-green' : 'gray-500';
        const statusIcon = miner.status === 'active' ? '🔥' : '💤';

        html += `
            <div class="flex items-center justify-between p-3 bg-glass-bg rounded-lg border border-glass-border">
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-${statusColor} rounded-full mr-3 ${miner.status === 'active' ? 'animate-pulse' : ''}"></div>
                    <div>
                        <div class="font-orbitron font-bold text-primary">${statusIcon} ${miner.id}</div>
                        <div class="text-sm text-muted">Tribe: ${miner.tribe} | Status: ${miner.status.toUpperCase()}</div>
                    </div>
                </div>
                <div class="text-right">
                    <div class="text-lg font-orbitron font-bold text-cyber-cyan">${miner.blocks_mined}</div>
                    <div class="text-xs text-muted">blocks mined</div>
                    ${miner.gpu_usage ? `<div class="text-xs text-cyber-yellow">GPU: ${miner.gpu_usage}%</div>` : ''}
                </div>
            </div>
        `;
    });

    html += '</div>';
    statusContainer.innerHTML = html;
}

// Show mining error state
function showMiningError() {
    const statusContainer = document.getElementById('live-mining-status');
    statusContainer.innerHTML = `
        <div class="text-center py-4">
            <i class="fas fa-exclamation-triangle text-cyber-yellow text-2xl mb-2"></i>
            <div class="text-muted">Unable to load mining status</div>
            <button onclick="loadMiningStatus()" class="glass-button-sm px-3 py-1 mt-2 text-xs">
                Try Again
            </button>
        </div>
    `;
}

// Manual refresh function
async function refreshNetworkData() {
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Refreshing...';
    button.disabled = true;

    await loadNetworkData();

    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    }, 1000);
}

// Manual mining status refresh
async function refreshMiningStatus() {
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Refreshing...';
    button.disabled = true;

    await loadMiningStatus();

    setTimeout(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    }, 1000);
}

// Search functionality
function performSearch() {
    const searchTerm = document.getElementById('blockchain-search').value.trim();
    if (!searchTerm) {
        if (typeof Onnyx !== 'undefined' && Onnyx.utils) {
            Onnyx.utils.showNotification('Please enter a search term', 'warning');
        }
        return;
    }

    // Show loading state
    const searchButton = event.target;
    const originalText = searchButton.textContent;
    searchButton.textContent = 'Searching...';
    searchButton.disabled = true;

    // Simulate search (replace with actual API call)
    setTimeout(() => {
        searchButton.textContent = originalText;
        searchButton.disabled = false;

        // For demo purposes, show a message
        if (searchTerm.length === 64) {
            if (typeof Onnyx !== 'undefined' && Onnyx.utils) {
                Onnyx.utils.showNotification('Block/Transaction hash detected', 'info');
            }
        } else if (searchTerm.length === 42) {
            if (typeof Onnyx !== 'undefined' && Onnyx.utils) {
                Onnyx.utils.showNotification('Address detected', 'info');
            }
        } else {
            if (typeof Onnyx !== 'undefined' && Onnyx.utils) {
                Onnyx.utils.showNotification('Search functionality coming soon', 'info');
            }
        }
    }, 1000);
}

// Auto-refresh blockchain data every 30 seconds
setInterval(async () => {
    try {
        // Refresh network stats
        const response = await fetch('/explorer/api/network-stats');
        const data = await response.json();

        if (data.success && data.blockchain) {
            const stats = data.blockchain;
            const latestBlockElement = document.getElementById('latest-block');
            const totalTxElement = document.getElementById('total-transactions');

            if (latestBlockElement) latestBlockElement.textContent = stats.latest_block || 0;
            if (totalTxElement) totalTxElement.textContent = stats.total_transactions || 0;
        }
    } catch (error) {
        console.log('Auto-refresh failed:', error);
    }
}, 30000);

// Enter key search
document.getElementById('blockchain-search')?.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        performSearch();
    }
});

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    stopNetworkMonitoring();
});
</script>
{% endblock %}
