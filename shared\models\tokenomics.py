"""
Biblical Tokenomics Module

This module implements the biblical economic principles for the Onnyx blockchain,
including jubilee resets, tiered mining rewards, gleaning pools, and anti-usury lending.
"""

import time
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timezone

from shared.db.db import db
from shared.config.chain_parameters import chain_parameters

logger = logging.getLogger("onnyx.tokenomics")

class BiblicalTokenomics:
    """
    Implements biblical economic principles for the Onnyx blockchain.
    """

    def __init__(self):
        """Initialize the Biblical Tokenomics system."""
        self.gleaning_pool_id = "GLEANS_POOL"
        self.firstfruits_pool_id = "FIRSTFRUITS_POOL"
        self.community_pool_id = "COMMUNITY_POOL"

    def calculate_tiered_mining_reward(self, proposer_id: str, base_reward: float) -> Tuple[float, float]:
        """
        Calculate mining reward with deed-based multipliers and gleaning pool allocation.

        Args:
            proposer_id: Identity ID of the block proposer
            base_reward: Base mining reward amount

        Returns:
            Tuple of (effective_reward, gleaning_allocation)
        """
        try:
            # Get proposer's deed score
            deed_score = self._get_deed_score(proposer_id)

            # Calculate deed bonus (max 10% bonus)
            deed_multiplier = chain_parameters.get("deed_score_multiplier", 0.1)
            deed_bonus = min(deed_score * deed_multiplier, deed_multiplier)

            # Check for concentration penalty
            concentration_penalty = self._check_concentration_penalty(proposer_id)

            # Calculate effective reward
            effective_reward = base_reward * (1 + deed_bonus) * concentration_penalty

            # Apply min/max limits
            min_reward = chain_parameters.get("min_block_reward", 2)
            max_reward = chain_parameters.get("max_block_reward", 200)
            effective_reward = max(min_reward, min(max_reward, effective_reward))

            # Calculate gleaning pool allocation (2% of base reward)
            gleaning_percentage = chain_parameters.get("gleaning_pool_percentage", 0.02)
            gleaning_allocation = base_reward * gleaning_percentage

            logger.info(f"Calculated reward for {proposer_id}: base={base_reward}, "
                       f"deed_bonus={deed_bonus:.3f}, penalty={concentration_penalty:.3f}, "
                       f"effective={effective_reward:.3f}, gleaning={gleaning_allocation:.3f}")

            return effective_reward, gleaning_allocation

        except Exception as e:
            logger.error(f"Error calculating tiered mining reward: {e}")
            return base_reward, 0.0

    def _get_deed_score(self, identity_id: str) -> float:
        """Get the current deed score for an identity."""
        try:
            # Get deed score from identities table
            result = db.query_one(
                "SELECT deeds_score FROM identities WHERE identity_id = ?",
                (identity_id,)
            )
            return result["deeds_score"] if result else 0.0
        except Exception as e:
            logger.warning(f"Could not get deed score for {identity_id}: {e}")
            return 0.0

    def _check_concentration_penalty(self, identity_id: str) -> float:
        """Check if identity should receive concentration penalty."""
        try:
            # Get total balance for identity
            result = db.query_one("""
                SELECT SUM(balance) as total_balance
                FROM token_balances
                WHERE identity_id = ? AND token_id = 'ONX'
            """, (identity_id,))

            total_balance = result["total_balance"] if result and result["total_balance"] else 0
            concentration_threshold = chain_parameters.get("concentration_threshold", 1000000)

            if total_balance > concentration_threshold:
                penalty_rate = chain_parameters.get("concentration_penalty_rate", 0.1)
                logger.info(f"Applying concentration penalty to {identity_id}: "
                           f"balance={total_balance}, threshold={concentration_threshold}")
                return penalty_rate

            return 1.0

        except Exception as e:
            logger.warning(f"Could not check concentration penalty for {identity_id}: {e}")
            return 1.0

    def allocate_to_gleaning_pool(self, amount: float, token_id: str = "ONX") -> bool:
        """
        Allocate tokens to the gleaning pool.

        Args:
            amount: Amount to allocate
            token_id: Token ID (default: ONX)

        Returns:
            True if successful
        """
        try:
            current_time = int(time.time())

            # Update gleaning pool balance
            db.execute("""
                INSERT OR REPLACE INTO jubilee_pools (pool_id, pool_type, total_amount, token_id, created_at, last_distribution)
                VALUES (?, 'GLEANING', COALESCE((SELECT total_amount FROM jubilee_pools WHERE pool_id = ?), 0) + ?, ?, ?, ?)
            """, (self.gleaning_pool_id, self.gleaning_pool_id, amount, token_id, current_time, current_time))

            logger.info(f"Allocated {amount} {token_id} to gleaning pool")
            return True

        except Exception as e:
            logger.error(f"Error allocating to gleaning pool: {e}")
            return False

    def record_deed(self, identity_id: str, deed_type: str, deed_value: float,
                   description: str = "", block_height: int = None) -> bool:
        """
        Record a righteous deed for an identity.

        Args:
            identity_id: Identity ID
            deed_type: Type of deed (MUTUAL_AID, DONATION, FIRSTFRUITS, SABBATH_OBSERVANCE)
            deed_value: Value/score of the deed
            description: Optional description
            block_height: Current block height

        Returns:
            True if successful
        """
        try:
            current_time = int(time.time())

            # Record the deed
            db.execute("""
                INSERT INTO deeds_ledger (identity_id, deed_type, deed_value, description, timestamp, block_height)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (identity_id, deed_type, deed_value, description, current_time, block_height))

            # Update identity's deed score
            self._update_deed_score(identity_id, deed_value)

            logger.info(f"Recorded deed for {identity_id}: {deed_type} = {deed_value}")
            return True

        except Exception as e:
            logger.error(f"Error recording deed: {e}")
            return False

    def _update_deed_score(self, identity_id: str, deed_value: float) -> None:
        """Update an identity's deed score."""
        try:
            db.execute("""
                UPDATE identities
                SET deeds_score = COALESCE(deeds_score, 0) + ?,
                    updated_at = ?
                WHERE identity_id = ?
            """, (deed_value, int(time.time()), identity_id))
        except Exception as e:
            logger.warning(f"Could not update deed score for {identity_id}: {e}")

    def update_activity_tracking(self, identity_id: str, block_height: int) -> None:
        """
        Update activity tracking for an identity.

        Args:
            identity_id: Identity ID
            block_height: Current block height
        """
        try:
            current_time = int(time.time())

            db.execute("""
                UPDATE identities
                SET last_active_timestamp = ?,
                    last_transaction_height = ?,
                    updated_at = ?
                WHERE identity_id = ?
            """, (current_time, block_height, current_time, identity_id))

        except Exception as e:
            logger.warning(f"Could not update activity tracking for {identity_id}: {e}")

    def check_dormant_accounts(self, current_block_height: int) -> List[str]:
        """
        Check for dormant accounts that should be subject to jubilee reclamation.

        Args:
            current_block_height: Current blockchain height

        Returns:
            List of dormant identity IDs
        """
        try:
            dormancy_threshold = chain_parameters.get("dormancy_threshold_blocks", 7200)
            threshold_height = current_block_height - dormancy_threshold

            dormant_accounts = db.query("""
                SELECT identity_id, last_transaction_height, last_active_timestamp
                FROM identities
                WHERE last_transaction_height < ? AND last_transaction_height > 0
            """, (threshold_height,))

            dormant_ids = [acc["identity_id"] for acc in dormant_accounts]

            if dormant_ids:
                logger.info(f"Found {len(dormant_ids)} dormant accounts at block {current_block_height}")

            return dormant_ids

        except Exception as e:
            logger.error(f"Error checking dormant accounts: {e}")
            return []

    def is_sabbath_period(self) -> bool:
        """
        Check if current time is within Sabbath period.

        Returns:
            True if it's currently Sabbath
        """
        try:
            now = datetime.now(timezone.utc)

            # Get Sabbath configuration
            sabbath_start_day = chain_parameters.get("sabbath_start_day", 5)  # Friday
            sabbath_start_hour = chain_parameters.get("sabbath_start_hour", 18)  # 6 PM
            sabbath_duration = chain_parameters.get("sabbath_duration_hours", 25)  # 25 hours

            # Calculate if we're in Sabbath period
            # This is a simplified implementation - in production you'd want proper timezone handling
            current_day = now.weekday()  # 0=Monday, 6=Sunday
            current_hour = now.hour

            if current_day == sabbath_start_day and current_hour >= sabbath_start_hour:
                return True
            elif current_day == (sabbath_start_day + 1) % 7 and current_hour < (sabbath_start_hour + sabbath_duration - 24):
                return True

            return False

        except Exception as e:
            logger.error(f"Error checking Sabbath period: {e}")
            return False

    # Feature 4: Anti-Usury Lending System

    def create_loan(self, lender_id: str, borrower_id: str, amount: float,
                   token_id: str = "ONX", grace_blocks: int = None) -> str:
        """
        Create a new interest-free loan.

        Args:
            lender_id: Identity ID of the lender
            borrower_id: Identity ID of the borrower
            amount: Loan amount
            token_id: Token ID (default: ONX)
            grace_blocks: Grace period in blocks

        Returns:
            Loan ID if successful
        """
        try:
            current_time = int(time.time())
            grace_blocks = grace_blocks or chain_parameters.get("loan_grace_blocks", 14400)
            loan_id = f"loan_{current_time}_{lender_id}_{borrower_id}"

            # Create loan record
            db.execute("""
                INSERT INTO loans (loan_id, lender_id, borrower_id, amount, token_id,
                                 grace_blocks, forgiveness_threshold, created_at, created_block)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (loan_id, lender_id, borrower_id, amount, token_id, grace_blocks,
                  chain_parameters.get("loan_forgiveness_threshold", 0.8), current_time, 0))

            # Transfer tokens from lender to borrower
            self._transfer_tokens(lender_id, borrower_id, amount, token_id)

            # Record deed for lender
            self.record_deed(lender_id, "LENDING", amount * 0.1, f"Provided loan of {amount} {token_id}")

            logger.info(f"Created loan {loan_id}: {amount} {token_id} from {lender_id} to {borrower_id}")
            return loan_id

        except Exception as e:
            logger.error(f"Error creating loan: {e}")
            return ""

    def repay_loan(self, loan_id: str, borrower_id: str, amount: float) -> bool:
        """
        Make a loan repayment.

        Args:
            loan_id: Loan ID
            borrower_id: Identity ID of the borrower
            amount: Repayment amount

        Returns:
            True if successful
        """
        try:
            # Get loan details
            loan = db.query_one("SELECT * FROM loans WHERE loan_id = ? AND status = 'ACTIVE'", (loan_id,))
            if not loan:
                logger.error(f"Loan {loan_id} not found or not active")
                return False

            if loan["borrower_id"] != borrower_id:
                logger.error(f"Borrower mismatch for loan {loan_id}")
                return False

            # Update loan payment
            new_amount_paid = loan["amount_paid"] + amount
            current_time = int(time.time())

            # Check if loan is fully repaid
            if new_amount_paid >= loan["amount"]:
                status = "REPAID"
                new_amount_paid = loan["amount"]  # Cap at loan amount
            else:
                status = "ACTIVE"

            db.execute("""
                UPDATE loans
                SET amount_paid = ?, status = ?, updated_at = ?
                WHERE loan_id = ?
            """, (new_amount_paid, status, current_time, loan_id))

            # Transfer repayment to lender
            self._transfer_tokens(borrower_id, loan["lender_id"], amount, loan["token_id"])

            # Record deed for borrower
            self.record_deed(borrower_id, "LOAN_REPAYMENT", amount * 0.05, f"Repaid {amount} on loan {loan_id}")

            logger.info(f"Loan repayment: {amount} on {loan_id}, total paid: {new_amount_paid}/{loan['amount']}")
            return True

        except Exception as e:
            logger.error(f"Error processing loan repayment: {e}")
            return False

    def check_loan_forgiveness(self, current_block: int) -> List[str]:
        """
        Check for loans eligible for automatic forgiveness.

        Args:
            current_block: Current block height

        Returns:
            List of forgiven loan IDs
        """
        try:
            forgiven_loans = []

            # Get active loans
            loans = db.query("SELECT * FROM loans WHERE status = 'ACTIVE'")

            for loan in loans:
                # Check if grace period has expired and threshold met
                blocks_elapsed = current_block - loan["created_block"]
                payment_ratio = loan["amount_paid"] / loan["amount"] if loan["amount"] > 0 else 0

                if (blocks_elapsed >= loan["grace_blocks"] and
                    payment_ratio >= loan["forgiveness_threshold"]):

                    # Forgive the loan
                    self.forgive_loan(loan["loan_id"], "SYSTEM_AUTO")
                    forgiven_loans.append(loan["loan_id"])

            if forgiven_loans:
                logger.info(f"Auto-forgave {len(forgiven_loans)} loans at block {current_block}")

            return forgiven_loans

        except Exception as e:
            logger.error(f"Error checking loan forgiveness: {e}")
            return []

    def forgive_loan(self, loan_id: str, forgiver_id: str) -> bool:
        """
        Forgive a loan (cancel remaining debt).

        Args:
            loan_id: Loan ID
            forgiver_id: Identity ID of forgiver (lender or SYSTEM_AUTO)

        Returns:
            True if successful
        """
        try:
            current_time = int(time.time())

            # Update loan status
            db.execute("""
                UPDATE loans
                SET status = 'FORGIVEN', updated_at = ?
                WHERE loan_id = ? AND status = 'ACTIVE'
            """, (current_time, loan_id))

            # Get loan details for deed recording
            loan = db.query_one("SELECT * FROM loans WHERE loan_id = ?", (loan_id,))
            if loan:
                remaining_debt = loan["amount"] - loan["amount_paid"]

                # Record deed for forgiveness
                if forgiver_id != "SYSTEM_AUTO":
                    self.record_deed(forgiver_id, "DEBT_FORGIVENESS", remaining_debt * 0.2,
                                   f"Forgave debt of {remaining_debt} on loan {loan_id}")

                # Record deed for borrower (debt relief)
                self.record_deed(loan["borrower_id"], "DEBT_RELIEF", remaining_debt * 0.1,
                               f"Received debt forgiveness of {remaining_debt} on loan {loan_id}")

            logger.info(f"Forgave loan {loan_id} by {forgiver_id}")
            return True

        except Exception as e:
            logger.error(f"Error forgiving loan: {e}")
            return False

    # Feature 5: Firstfruits Offering Mechanism

    def make_firstfruits_offering(self, offerer_id: str, amount: float, token_id: str = "ONX") -> bool:
        """
        Make a firstfruits offering to the community.

        Args:
            offerer_id: Identity ID of the offerer
            amount: Offering amount
            token_id: Token ID (default: ONX)

        Returns:
            True if successful
        """
        try:
            current_time = int(time.time())

            # Transfer offering to firstfruits pool
            self._transfer_tokens(offerer_id, "FIRSTFRUITS_POOL", amount, token_id)

            # Add to firstfruits pool
            db.execute("""
                INSERT OR REPLACE INTO jubilee_pools (pool_id, pool_type, total_amount, token_id, created_at, last_distribution)
                VALUES ('FIRSTFRUITS_POOL', 'FIRSTFRUITS',
                        COALESCE((SELECT total_amount FROM jubilee_pools WHERE pool_id = 'FIRSTFRUITS_POOL'), 0) + ?,
                        ?, ?, ?)
            """, (amount, token_id, current_time, current_time))

            # Record the deed
            self.record_deed(offerer_id, "FIRSTFRUITS", amount, f"Firstfruits offering of {amount} {token_id}")

            # Award Etzem tokens
            etzem_reward = chain_parameters.get("firstfruits_etzem_reward", 2)
            self._mint_tokens(offerer_id, etzem_reward, "ETZEM")

            # Assign Etzem token class if not already assigned
            self.assign_token_class("ETZEM", "Etzem")

            logger.info(f"Firstfruits offering: {amount} {token_id} from {offerer_id}, awarded {etzem_reward} ETZEM")
            return True

        except Exception as e:
            logger.error(f"Error processing firstfruits offering: {e}")
            return False

    # Feature 6: Anti-Concentration Protocol

    def enforce_concentration_limits(self, identity_id: str, current_block: int) -> bool:
        """
        Enforce anti-concentration protocol for an identity.

        Args:
            identity_id: Identity ID to check
            current_block: Current block height

        Returns:
            True if concentration limits enforced
        """
        try:
            concentration_threshold = chain_parameters.get("concentration_threshold", 1000000)

            # Get total balance across all tokens
            total_balance = self._get_total_balance(identity_id)

            if total_balance > concentration_threshold:
                # Apply concentration penalty
                penalty_rate = chain_parameters.get("concentration_penalty_rate", 0.1)

                # Record concentration violation
                self.record_deed(identity_id, "CONCENTRATION_VIOLATION", -total_balance * 0.01,
                               f"Exceeded concentration threshold: {total_balance}")

                # Suggest redistribution to gleaning pool
                excess_amount = total_balance - concentration_threshold
                suggested_donation = excess_amount * 0.1  # Suggest 10% of excess

                logger.warning(f"Concentration limit exceeded by {identity_id}: {total_balance} > {concentration_threshold}")
                logger.info(f"Suggested gleaning pool donation: {suggested_donation}")

                return True

            return False

        except Exception as e:
            logger.error(f"Error enforcing concentration limits: {e}")
            return False

    # Feature 7: Biblical Token Classification System

    def assign_token_class(self, token_id: str, class_type: str, metadata: str = "{}") -> bool:
        """
        Assign a biblical class to a token.

        Args:
            token_id: Token ID
            class_type: Class type (Avodah, Zedek, Yovel, Etzem)
            metadata: Additional metadata

        Returns:
            True if successful
        """
        try:
            valid_classes = ["Avodah", "Zedek", "Yovel", "Etzem"]
            if class_type not in valid_classes:
                logger.error(f"Invalid token class: {class_type}")
                return False

            current_time = int(time.time())

            db.execute("""
                INSERT OR REPLACE INTO token_classes (token_id, class_type, class_metadata, assigned_at)
                VALUES (?, ?, ?, ?)
            """, (token_id, class_type, metadata, current_time))

            logger.info(f"Assigned token class {class_type} to {token_id}")
            return True

        except Exception as e:
            logger.error(f"Error assigning token class: {e}")
            return False

    def get_token_class(self, token_id: str) -> str:
        """
        Get the biblical class of a token.

        Args:
            token_id: Token ID

        Returns:
            Token class or empty string if not classified
        """
        try:
            result = db.query_one("SELECT class_type FROM token_classes WHERE token_id = ?", (token_id,))
            return result["class_type"] if result else ""
        except Exception as e:
            logger.error(f"Error getting token class: {e}")
            return ""

    def classify_token_by_purpose(self, token_id: str, purpose: str) -> bool:
        """
        Automatically classify a token based on its purpose.

        Args:
            token_id: Token ID
            purpose: Token purpose (labor, righteousness, jubilee, essence)

        Returns:
            True if classified successfully
        """
        try:
            purpose_to_class = {
                "labor": "Avodah",
                "work": "Avodah",
                "service": "Avodah",
                "righteousness": "Zedek",
                "justice": "Zedek",
                "charity": "Zedek",
                "jubilee": "Yovel",
                "redistribution": "Yovel",
                "reset": "Yovel",
                "essence": "Etzem",
                "core": "Etzem",
                "fundamental": "Etzem"
            }

            class_type = purpose_to_class.get(purpose.lower())
            if class_type:
                return self.assign_token_class(token_id, class_type, f'{{"purpose": "{purpose}"}}')

            logger.warning(f"Unknown purpose for token classification: {purpose}")
            return False

        except Exception as e:
            logger.error(f"Error classifying token by purpose: {e}")
            return False

    # Feature 8: Minimum/Maximum Wage Logic (already implemented in calculate_tiered_mining_reward)

    def enforce_reward_bounds(self, calculated_reward: float) -> float:
        """
        Enforce minimum and maximum reward bounds.

        Args:
            calculated_reward: Calculated reward amount

        Returns:
            Bounded reward amount
        """
        min_reward = chain_parameters.get("min_block_reward", 2)
        max_reward = chain_parameters.get("max_block_reward", 200)

        bounded_reward = max(min_reward, min(max_reward, calculated_reward))

        if bounded_reward != calculated_reward:
            logger.info(f"Reward bounded: {calculated_reward} -> {bounded_reward}")

        return bounded_reward

    # Feature 9: Sabbath Enforcement and Righteous Boost

    def record_sabbath_observance(self, identity_id: str, block_height: int) -> bool:
        """
        Record Sabbath observance for an identity.

        Args:
            identity_id: Identity ID
            block_height: Current block height

        Returns:
            True if recorded successfully
        """
        try:
            # Award sabbath deed bonus
            sabbath_bonus = chain_parameters.get("sabbath_deed_bonus", 0.2)

            self.record_deed(identity_id, "SABBATH_OBSERVANCE", sabbath_bonus,
                           f"Observed Sabbath at block {block_height}")

            # Mark as sabbath observer
            db.execute("""
                UPDATE identities
                SET sabbath_observer = 1, updated_at = ?
                WHERE identity_id = ?
            """, (int(time.time()), identity_id))

            logger.info(f"Recorded Sabbath observance for {identity_id}")
            return True

        except Exception as e:
            logger.error(f"Error recording Sabbath observance: {e}")
            return False

    def get_sabbath_observers(self) -> List[str]:
        """
        Get list of current Sabbath observers.

        Returns:
            List of identity IDs who observe Sabbath
        """
        try:
            observers = db.query("SELECT identity_id FROM identities WHERE sabbath_observer = 1")
            return [obs["identity_id"] for obs in observers]
        except Exception as e:
            logger.error(f"Error getting Sabbath observers: {e}")
            return []

    def start_sabbath_period(self, block_height: int) -> bool:
        """
        Start a new Sabbath period.

        Args:
            block_height: Current block height

        Returns:
            True if started successfully
        """
        try:
            current_time = int(time.time())
            sabbath_duration = chain_parameters.get("sabbath_duration_hours", 25)
            end_time = current_time + (sabbath_duration * 3600)  # Convert hours to seconds

            db.execute("""
                INSERT INTO sabbath_periods (start_timestamp, end_timestamp, block_height_start)
                VALUES (?, ?, ?)
            """, (current_time, end_time, block_height))

            logger.info(f"Started Sabbath period at block {block_height}")
            return True

        except Exception as e:
            logger.error(f"Error starting Sabbath period: {e}")
            return False

    # Helper methods

    def _transfer_tokens(self, from_id: str, to_id: str, amount: float, token_id: str) -> bool:
        """Transfer tokens between identities."""
        try:
            current_time = int(time.time())

            # Deduct from sender
            db.execute("""
                UPDATE token_balances
                SET balance = balance - ?, updated_at = ?
                WHERE identity_id = ? AND token_id = ?
            """, (amount, current_time, from_id, token_id))

            # Credit to receiver
            db.execute("""
                INSERT OR REPLACE INTO token_balances (identity_id, token_id, balance, updated_at)
                VALUES (?, ?, COALESCE((SELECT balance FROM token_balances WHERE identity_id = ? AND token_id = ?), 0) + ?, ?)
            """, (to_id, token_id, to_id, token_id, amount, current_time))

            return True
        except Exception as e:
            logger.error(f"Error transferring tokens: {e}")
            return False

    def _mint_tokens(self, to_id: str, amount: float, token_id: str) -> bool:
        """Mint new tokens to an identity."""
        try:
            current_time = int(time.time())

            db.execute("""
                INSERT OR REPLACE INTO token_balances (identity_id, token_id, balance, updated_at)
                VALUES (?, ?, COALESCE((SELECT balance FROM token_balances WHERE identity_id = ? AND token_id = ?), 0) + ?, ?)
            """, (to_id, token_id, to_id, token_id, amount, current_time))

            return True
        except Exception as e:
            logger.error(f"Error minting tokens: {e}")
            return False

    def _get_total_balance(self, identity_id: str) -> float:
        """Get total balance across all tokens for an identity."""
        try:
            result = db.query_one("""
                SELECT SUM(balance) as total_balance
                FROM token_balances
                WHERE identity_id = ?
            """, (identity_id,))

            return result["total_balance"] if result and result["total_balance"] else 0.0
        except Exception as e:
            logger.error(f"Error getting total balance: {e}")
            return 0.0

# Global instance
biblical_tokenomics = BiblicalTokenomics()
