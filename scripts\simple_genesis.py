#!/usr/bin/env python3
"""
ONNYX Simple Genesis Script
Creates founder and basic ecosystem without complex resets
"""

import sys
import os
import time
import json
from datetime import datetime

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from shared.db.db import db
from shared.models.identity import Identity
from shared.models.sela import Sela
from shared.models.transaction import Transaction

def check_and_create_founder():
    """Check if founder exists, create if not"""
    print("👑 Checking founder identity...")

    # Check if founder already exists
    existing_founder = db.query_one("SELECT * FROM identities WHERE email = ?", ("<EMAIL>",))

    if existing_founder:
        print(f"✅ Founder already exists: {existing_founder['name']} ({existing_founder['email']})")
        return existing_founder

    # Create new founder
    print("🌟 Creating new founder identity...")

    founder = Identity(
        identity_id="founder_jedidiah_israel",
        name="Jedidiah Israel",
        public_key="founder_public_key_placeholder",  # Required parameter
        email="<EMAIL>",
        role_class="Citizen",
        verification_level=3,
        nation_code="JU",
        created_at=int(time.time())
    )

    try:
        founder.save()
        print(f"✅ Founder created: {founder.name}")

        # Try to create token balance
        try:
            # Check if token_balances table exists and has the founder
            existing_balance = db.query_one(
                "SELECT * FROM token_balances WHERE identity_id = ? AND token_class = ?",
                (founder.identity_id, "ONX")
            )

            if not existing_balance:
                db.execute("""
                    INSERT OR REPLACE INTO token_balances (identity_id, token_class, balance, last_updated)
                    VALUES (?, ?, ?, ?)
                """, (founder.identity_id, "ONX", 1000.0, int(time.time())))
                print("✅ Founder token balance created: 1000 ONX")
            else:
                print(f"✅ Founder token balance exists: {existing_balance['balance']} ONX")

        except Exception as e:
            print(f"⚠️ Could not create token balance: {e}")

        return founder

    except Exception as e:
        print(f"❌ Error creating founder: {e}")
        return None

def create_sample_participants():
    """Create a few sample participants"""
    print("👥 Creating sample participants...")

    participants = [
        ("Abraham ben David", "<EMAIL>", "JU", 2),
        ("Sarah bat Rachel", "<EMAIL>", "LE", 1),
        ("Isaac ben Abraham", "<EMAIL>", "EP", 1),
        ("Rebecca bat Sarah", "<EMAIL>", "BE", 0),
        ("Jacob ben Isaac", "<EMAIL>", "SI", 2)
    ]

    created_participants = []

    for i, (name, email, tribal_code, tier) in enumerate(participants):
        # Check if participant already exists
        existing = db.query_one("SELECT * FROM identities WHERE email = ?", (email,))

        if existing:
            print(f"   ✅ {name} already exists")
            created_participants.append(existing)
            continue

        try:
            participant = Identity(
                identity_id=f"participant_{i+1:03d}_{name.split()[0].lower()}",
                name=name,
                public_key=f"participant_{i+1}_public_key_placeholder",  # Required parameter
                email=email,
                role_class="Citizen",
                verification_level=tier,
                nation_code=tribal_code,
                created_at=int(time.time()) + i
            )
            participant.save()

            # Try to create token balance
            initial_balance = {0: 10.0, 1: 50.0, 2: 200.0, 3: 500.0}[tier]
            try:
                db.execute("""
                    INSERT OR REPLACE INTO token_balances (identity_id, token_class, balance, last_updated)
                    VALUES (?, ?, ?, ?)
                """, (participant.identity_id, "ONX", initial_balance, int(time.time())))
            except Exception as e:
                print(f"   ⚠️ Token balance warning for {name}: {e}")

            created_participants.append(participant)
            print(f"   ✅ {name} ({tribal_code}) - Tier {tier} - {initial_balance} ONX")

        except Exception as e:
            print(f"   ❌ Error creating {name}: {e}")

    print(f"✅ Participants ready: {len(created_participants)} total")
    return created_participants

def create_sample_organization():
    """Create a sample organization"""
    print("🏢 Creating sample organization...")

    # Check if organization already exists
    existing_org = db.query_one("SELECT * FROM selas WHERE sela_id = ?", ("covenant_tech_solutions",))

    if existing_org:
        print(f"✅ Organization already exists: {existing_org['name']}")
        return existing_org

    # Get founder for organization owner
    founder = db.query_one("SELECT * FROM identities WHERE email = ?", ("<EMAIL>",))

    if not founder:
        print("❌ Cannot create organization - founder not found")
        return None

    try:
        org = Sela(
            sela_id="covenant_tech_solutions",
            name="Covenant Tech Solutions",
            founder_id=founder['identity_id'],  # Use founder_id instead of identity_id
            sela_type="Technology Services",  # Use sela_type instead of category
            created_at=int(time.time())
        )
        org.save()

        print(f"✅ Organization created: {org.name}")
        return org

    except Exception as e:
        print(f"❌ Error creating organization: {e}")
        return None

def create_genesis_transaction():
    """Create a simple genesis transaction"""
    print("📜 Creating genesis transaction...")

    # Check if genesis transaction already exists
    existing_tx = db.query_one("SELECT * FROM transactions WHERE tx_id = ?", ("simple_genesis_founding",))

    if existing_tx:
        print(f"✅ Genesis transaction already exists: {existing_tx['tx_id']}")
        return existing_tx

    try:
        genesis_tx = Transaction(
            tx_id="simple_genesis_founding",
            op="OP_COVENANT_FOUNDING",
            sender="SYSTEM",
            signature="system_genesis_signature_placeholder",  # Required parameter
            data=json.dumps({
                "covenant_name": "ONNYX Covenant Blockchain",
                "founding_date": datetime.now().isoformat(),
                "founder": "<EMAIL>",
                "biblical_principles": ["anti_usury", "sabbath_observance", "gleaning_pools"]
            }),
            timestamp=int(time.time()),
            status="confirmed"
        )
        genesis_tx.save()

        print(f"✅ Genesis transaction created: {genesis_tx.tx_id}")
        return genesis_tx

    except Exception as e:
        print(f"❌ Error creating genesis transaction: {e}")
        return None

def display_ecosystem_status():
    """Display current ecosystem status"""
    print("\n🌟 ONNYX ECOSYSTEM STATUS")
    print("=" * 50)

    # Count identities
    identity_count = db.query_one("SELECT COUNT(*) as count FROM identities")
    print(f"👥 Identities: {identity_count['count'] if identity_count else 0}")

    # Count organizations
    if db.table_exists('selas'):
        org_count = db.query_one("SELECT COUNT(*) as count FROM selas")
        print(f"🏢 Organizations: {org_count['count'] if org_count else 0}")

    # Count transactions
    tx_count = db.query_one("SELECT COUNT(*) as count FROM transactions")
    print(f"📜 Transactions: {tx_count['count'] if tx_count else 0}")

    # Show founder status
    founder = db.query_one("SELECT * FROM identities WHERE email = ?", ("<EMAIL>",))
    if founder:
        print(f"\n👑 FOUNDER STATUS:")
        print(f"   Name: {founder['name']}")
        print(f"   Email: {founder['email']}")
        print(f"   Tier: {founder['verification_level']}")
        print(f"   Tribe: {founder['nation_code']}")

        # Check token balance
        if db.table_exists('token_balances'):
            try:
                # First check what columns exist
                columns = db.query("PRAGMA table_info(token_balances)")
                column_names = [col[1] for col in columns]

                if 'token_class' in column_names:
                    balance = db.query_one(
                        "SELECT balance FROM token_balances WHERE identity_id = ? AND token_class = ?",
                        (founder['identity_id'], "ONX")
                    )
                elif 'token_id' in column_names:
                    balance = db.query_one(
                        "SELECT balance FROM token_balances WHERE identity_id = ? AND token_id = ?",
                        (founder['identity_id'], "ONX")
                    )
                else:
                    balance = db.query_one(
                        "SELECT balance FROM token_balances WHERE identity_id = ?",
                        (founder['identity_id'],)
                    )

                if balance:
                    print(f"   Balance: {balance['balance']} ONX")
            except Exception as e:
                print(f"   ⚠️ Could not check token balance: {e}")

    print(f"\n🌐 ACCESS YOUR COVENANT BLOCKCHAIN:")
    print(f"   Web Interface: http://localhost:5000")
    print(f"   Blockchain Explorer: http://localhost:5000/explorer/")
    print(f"   Login as Founder: <EMAIL>")

def main():
    """Main execution function"""
    print("🚀 ONNYX SIMPLE GENESIS")
    print("=" * 40)

    try:
        # Step 1: Create founder
        founder = check_and_create_founder()
        if not founder:
            print("❌ Failed to create founder - aborting")
            return

        # Step 2: Create sample participants
        participants = create_sample_participants()

        # Step 3: Create sample organization
        organization = create_sample_organization()

        # Step 4: Create genesis transaction
        genesis_tx = create_genesis_transaction()

        # Step 5: Display status
        display_ecosystem_status()

        print("\n🎉 SIMPLE GENESIS COMPLETED!")
        print("Your ONNYX covenant blockchain has basic participants and is ready for testing!")

    except Exception as e:
        print(f"\n❌ Genesis failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
