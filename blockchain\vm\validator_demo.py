"""
Onnyx VM Validator Demo

This script demonstrates how to use the validator module to validate
transactions in a real-world scenario.
"""

import sys
import os
import json
import time
import uuid
import hashlib

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Add the VM directory to the Python path
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "vm"))

from validator import (
    validate_transaction,
    validate_transaction_safe,
    validate_transactions,
    ValidationError,
    format_validation_error,
    get_transaction_type,
    summarize_transaction
)

from opcodes import (
    OP_MINT, OP_SEND, OP_IDENTITY, OP_SCROLL,
    identity, selas, tokens, ledger
)

def setup_demo_data():
    """Set up demo data for the validator demo."""
    print("Setting up demo data...")
    
    # Set up identities
    identity.identities["alice"] = {
        "name": "<PERSON>",
        "public_key": "0x123456789abcdef",
        "created_at": int(time.time())
    }
    
    identity.identities["bob"] = {
        "name": "<PERSON>",
        "public_key": "0x987654321fedcba",
        "created_at": int(time.time())
    }
    
    # Set up badges
    identity.badges["alice"] = ["CREATOR", "VALIDATOR"]
    identity.badges["bob"] = ["CREATOR"]
    
    # Set up Selas
    selas.selas["alice"] = {
        "name": "Alice's Business",
        "type": "service",
        "tier": "gold",
        "created_at": int(time.time())
    }
    
    # Set up tokens
    tokens.tokens["ALICE"] = {
        "symbol": "ALICE",
        "name": "Alice Token",
        "creator": "alice",
        "supply": 10000,
        "created_at": int(time.time())
    }
    
    # Set up ledger
    ledger.balances["alice:ALICE"] = 10000
    
    print("Demo data setup complete.")

def generate_txid():
    """Generate a unique transaction ID."""
    return hashlib.sha256(str(uuid.uuid4()).encode()).hexdigest()[:16]

def demo_transaction_validation():
    """Demonstrate transaction validation."""
    print("\n=== Transaction Validation ===")
    
    # Create a list of transactions
    txs = [
        # Valid mint transaction
        {
            "op": OP_MINT,
            "from": "alice",
            "data": {
                "symbol": "BOB",
                "supply": 1000
            }
        },
        # Valid send transaction
        {
            "op": OP_SEND,
            "from": "alice",
            "data": {
                "token_id": "ALICE",
                "to": "bob",
                "amount": 500
            }
        },
        # Invalid send transaction (insufficient balance)
        {
            "op": OP_SEND,
            "from": "bob",
            "data": {
                "token_id": "ALICE",
                "to": "alice",
                "amount": 100
            }
        },
        # Invalid mint transaction (not a Sela)
        {
            "op": OP_MINT,
            "from": "bob",
            "data": {
                "symbol": "BOB2",
                "supply": 1000
            }
        },
        # Valid identity transaction
        {
            "op": OP_IDENTITY,
            "data": {
                "identity_id": "charlie",
                "name": "Charlie",
                "public_key": "0xabcdef123456789"
            }
        },
        # Valid scroll transaction
        {
            "op": OP_SCROLL,
            "from": "alice",
            "data": {
                "title": "Increase Yovel Mint Cap",
                "description": "Proposal to increase the Yovel mint cap from 1000 to 2000 tokens.",
                "category": "economic"
            }
        }
    ]
    
    # Validate all transactions
    valid_txs, invalid_txs = validate_transactions(txs)
    
    print(f"Valid transactions: {len(valid_txs)}")
    print(f"Invalid transactions: {len(invalid_txs)}")
    
    # Process valid transactions
    print("\nProcessing valid transactions:")
    for tx in valid_txs:
        summary = summarize_transaction(tx)
        print(f"\n- {summary['description']}")
        print(f"  Type: {summary['type']}")
        print(f"  From: {summary['from']}")
    
    # Report invalid transactions
    print("\nInvalid transactions:")
    for result in invalid_txs:
        tx = result["tx"]
        error = result["error"]
        summary = summarize_transaction(tx)
        print(f"\n- {summary['description']}")
        print(f"  Type: {summary['type']}")
        print(f"  From: {summary['from']}")
        print(f"  Error: {error}")

def demo_transaction_processing():
    """Demonstrate transaction processing with validation."""
    print("\n=== Transaction Processing ===")
    
    # Create a transaction
    tx = {
        "op": OP_SEND,
        "from": "alice",
        "data": {
            "token_id": "ALICE",
            "to": "bob",
            "amount": 500
        }
    }
    
    print("Processing transaction:")
    summary = summarize_transaction(tx)
    print(f"- {summary['description']}")
    
    # Validate the transaction
    try:
        validate_transaction(tx)
        print("Transaction is valid.")
        
        # Process the transaction (in a real system, this would update the ledger)
        ledger.balances["alice:ALICE"] -= 500
        ledger.balances["bob:ALICE"] = ledger.balances.get("bob:ALICE", 0) + 500
        
        print("Transaction processed successfully.")
        print(f"Alice's balance: {ledger.balances['alice:ALICE']} ALICE")
        print(f"Bob's balance: {ledger.balances['bob:ALICE']} ALICE")
    except ValidationError as e:
        print(f"Transaction is invalid: {e}")
        formatted_error = format_validation_error(str(e))
        print(f"Error details: {json.dumps(formatted_error, indent=2)}")

def demo_batch_processing():
    """Demonstrate batch transaction processing."""
    print("\n=== Batch Transaction Processing ===")
    
    # Create a batch of transactions
    batch = [
        {
            "op": OP_SEND,
            "from": "alice",
            "data": {
                "token_id": "ALICE",
                "to": "bob",
                "amount": 100
            }
        },
        {
            "op": OP_SEND,
            "from": "alice",
            "data": {
                "token_id": "ALICE",
                "to": "bob",
                "amount": 200
            }
        },
        {
            "op": OP_SEND,
            "from": "bob",
            "data": {
                "token_id": "ALICE",
                "to": "alice",
                "amount": 50
            }
        }
    ]
    
    print(f"Processing batch of {len(batch)} transactions...")
    
    # Validate all transactions in the batch
    valid_txs, invalid_txs = validate_transactions(batch)
    
    print(f"Valid transactions: {len(valid_txs)}")
    print(f"Invalid transactions: {len(invalid_txs)}")
    
    # Process valid transactions
    if valid_txs:
        print("\nProcessing valid transactions:")
        for tx in valid_txs:
            summary = summarize_transaction(tx)
            print(f"\n- {summary['description']}")
            
            # Process the transaction (in a real system, this would update the ledger)
            from_id = tx["from"]
            to_id = tx["data"]["to"]
            token_id = tx["data"]["token_id"]
            amount = tx["data"]["amount"]
            
            # Update balances
            from_balance_key = f"{from_id}:{token_id}"
            to_balance_key = f"{to_id}:{token_id}"
            
            ledger.balances[from_balance_key] = ledger.balances.get(from_balance_key, 0) - amount
            ledger.balances[to_balance_key] = ledger.balances.get(to_balance_key, 0) + amount
            
            print(f"  {from_id}'s balance: {ledger.balances[from_balance_key]} {token_id}")
            print(f"  {to_id}'s balance: {ledger.balances[to_balance_key]} {token_id}")
    
    # Report invalid transactions
    if invalid_txs:
        print("\nInvalid transactions:")
        for result in invalid_txs:
            tx = result["tx"]
            error = result["error"]
            summary = summarize_transaction(tx)
            print(f"\n- {summary['description']}")
            print(f"  Error: {error}")

def main():
    """Run the validator demo."""
    print("Onnyx VM Validator Demo")
    print("=======================")
    
    # Set up demo data
    setup_demo_data()
    
    # Run demo scenarios
    demo_transaction_validation()
    demo_transaction_processing()
    demo_batch_processing()
    
    print("\nDemo completed.")

if __name__ == "__main__":
    main()
