/**
 * ONNYX Sela Dashboard JavaScript
 * Enhanced functionality for the biblical tokenomics dashboard
 */

class SelaDashboard {
    constructor() {
        this.selaId = window.selaId || null;
        this.updateInterval = 30000; // 30 seconds
        this.init();
    }

    init() {
        console.log('🏢 Sela Dashboard initializing...');
        
        this.initTabSwitching();
        this.initVerificationUpdates();
        this.initAnimations();
        this.initQuickActions();
        this.initFlagFallbacks();
        
        console.log('✅ Sela Dashboard ready');
    }

    /**
     * Initialize tab switching functionality
     */
    initTabSwitching() {
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');
        
        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const tabName = button.dataset.tab;
                
                // Remove active class from all buttons
                tabButtons.forEach(btn => btn.classList.remove('active'));
                
                // Hide all tab contents
                tabContents.forEach(content => content.classList.add('hidden'));
                
                // Activate clicked button
                button.classList.add('active');
                
                // Show corresponding tab content
                const targetTab = document.getElementById(tabName + '-tab');
                if (targetTab) {
                    targetTab.classList.remove('hidden');
                    
                    // Trigger animations for newly visible content
                    this.animateTabContent(targetTab);
                }
            });
        });
    }

    /**
     * Initialize real-time verification status updates
     */
    initVerificationUpdates() {
        if (!this.selaId) return;
        
        // Update verification status every 30 seconds
        setInterval(() => {
            this.updateVerificationStatus();
        }, this.updateInterval);
    }

    /**
     * Update verification status from API
     */
    async updateVerificationStatus() {
        try {
            const response = await fetch(`/api/sela/${this.selaId}/verification`);
            if (!response.ok) return;
            
            const data = await response.json();
            
            // Update verification indicators
            Object.keys(data).forEach(key => {
                const element = document.querySelector(`[data-verification="${key}"]`);
                if (element) {
                    const isVerified = data[key];
                    element.className = `w-8 h-8 rounded-full flex items-center justify-center ${
                        isVerified ? 'bg-cyber-green' : 'bg-cyber-red'
                    }`;
                    
                    // Update icon
                    const icon = isVerified ? '✅' : '❌';
                    element.innerHTML = `<span class="text-onyx-black">${icon}</span>`;
                }
            });
            
            // Update overall score
            const scoreElement = document.querySelector('.verification-score');
            if (scoreElement && data.overall_score !== undefined) {
                scoreElement.textContent = `${data.overall_score}%`;
                
                const progressBar = document.querySelector('.verification-progress');
                if (progressBar) {
                    progressBar.style.width = `${data.overall_score}%`;
                }
            }
            
        } catch (error) {
            console.log('Verification update failed:', error);
        }
    }

    /**
     * Initialize animations
     */
    initAnimations() {
        // Animate Etzem score on load
        this.animateEtzemScore();
        
        // Animate verification cards
        this.animateVerificationCards();
        
        // Animate participant cards
        this.animateParticipantCards();
    }

    /**
     * Animate Etzem score progress bar
     */
    animateEtzemScore() {
        const progressBar = document.querySelector('.etzem-progress');
        if (progressBar) {
            const targetWidth = progressBar.style.width;
            progressBar.style.width = '0%';
            progressBar.style.transition = 'width 1s ease-out';
            
            setTimeout(() => {
                progressBar.style.width = targetWidth;
            }, 500);
        }
    }

    /**
     * Animate verification cards on load
     */
    animateVerificationCards() {
        const cards = document.querySelectorAll('.verification-item');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'all 0.5s ease-out';
            
            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }

    /**
     * Animate participant cards
     */
    animateParticipantCards() {
        const cards = document.querySelectorAll('.participant-card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'scale(0.9)';
            card.style.transition = 'all 0.5s ease-out';
            
            setTimeout(() => {
                card.style.opacity = '1';
                card.style.transform = 'scale(1)';
            }, index * 150);
        });
    }

    /**
     * Animate tab content when switching
     */
    animateTabContent(tabElement) {
        const items = tabElement.querySelectorAll('.participant-card, .service-log-item');
        items.forEach((item, index) => {
            item.style.opacity = '0';
            item.style.transform = 'translateX(-20px)';
            item.style.transition = 'all 0.3s ease-out';
            
            setTimeout(() => {
                item.style.opacity = '1';
                item.style.transform = 'translateX(0)';
            }, index * 50);
        });
    }

    /**
     * Initialize quick action buttons
     */
    initQuickActions() {
        // Mine Block button
        const mineButton = document.querySelector('[data-action="mine-block"]');
        if (mineButton) {
            mineButton.addEventListener('click', () => this.mineBlock());
        }
        
        // Submit Scroll button
        const scrollButton = document.querySelector('[data-action="submit-scroll"]');
        if (scrollButton) {
            scrollButton.addEventListener('click', () => this.submitScroll());
        }
        
        // New Contract button
        const contractButton = document.querySelector('[data-action="new-contract"]');
        if (contractButton) {
            contractButton.addEventListener('click', () => this.newContract());
        }
        
        // View Analytics button
        const analyticsButton = document.querySelector('[data-action="view-analytics"]');
        if (analyticsButton) {
            analyticsButton.addEventListener('click', () => this.viewAnalytics());
        }
    }

    /**
     * Initialize flag image fallbacks
     */
    initFlagFallbacks() {
        const flagImages = document.querySelectorAll('.nation-flag');
        flagImages.forEach(img => {
            img.addEventListener('error', () => {
                // Create a simple colored rectangle as fallback
                const canvas = document.createElement('canvas');
                canvas.width = 24;
                canvas.height = 16;
                const ctx = canvas.getContext('2d');
                
                // Generate a color based on the country code
                const countryCode = img.alt || 'XX';
                const hue = countryCode.charCodeAt(0) * 137.508; // Golden angle
                ctx.fillStyle = `hsl(${hue % 360}, 70%, 50%)`;
                ctx.fillRect(0, 0, 24, 16);
                
                // Add country code text
                ctx.fillStyle = 'white';
                ctx.font = '8px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(countryCode.substring(0, 2).toUpperCase(), 12, 11);
                
                img.src = canvas.toDataURL();
            });
        });
    }

    /**
     * Mine a block
     */
    async mineBlock() {
        try {
            const response = await fetch('/api/mining/mine', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ sela_id: this.selaId })
            });
            
            if (response.ok) {
                const result = await response.json();
                this.showNotification('Block mined successfully!', 'success');
                
                // Update mining stats
                this.updateMiningStats(result);
            } else {
                this.showNotification('Mining failed. Please try again.', 'error');
            }
        } catch (error) {
            console.error('Mining error:', error);
            this.showNotification('Mining error occurred.', 'error');
        }
    }

    /**
     * Submit a governance scroll
     */
    submitScroll() {
        // Placeholder for scroll submission
        this.showNotification('Scroll submission feature coming soon!', 'info');
    }

    /**
     * Create a new contract
     */
    newContract() {
        // Placeholder for contract creation
        this.showNotification('Contract creation feature coming soon!', 'info');
    }

    /**
     * View analytics
     */
    viewAnalytics() {
        // Redirect to analytics page
        window.location.href = `/sela/${this.selaId}/analytics`;
    }

    /**
     * Update mining statistics
     */
    updateMiningStats(result) {
        // Update ONX balance
        const balanceElement = document.querySelector('.onx-balance');
        if (balanceElement && result.new_balance) {
            balanceElement.textContent = result.new_balance.toFixed(1);
        }
        
        // Update blocks mined
        const blocksElement = document.querySelector('.blocks-mined');
        if (blocksElement && result.total_blocks) {
            blocksElement.textContent = result.total_blocks;
        }
        
        // Update mining rewards
        const rewardsElement = document.querySelector('.mining-rewards');
        if (rewardsElement && result.total_rewards) {
            rewardsElement.textContent = result.total_rewards.toFixed(2);
        }
    }

    /**
     * Show notification
     */
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 ${
            type === 'success' ? 'bg-cyber-green text-onyx-black' :
            type === 'error' ? 'bg-cyber-red text-white' :
            type === 'warning' ? 'bg-cyber-yellow text-onyx-black' :
            'bg-cyber-blue text-white'
        }`;
        notification.textContent = message;
        
        // Add to page
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
            notification.style.opacity = '1';
        }, 100);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            notification.style.opacity = '0';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    /**
     * Refresh dashboard data
     */
    async refreshDashboard() {
        try {
            const response = await fetch(`/sela/${this.selaId}/dashboard-data`);
            if (response.ok) {
                const data = await response.json();
                this.updateDashboardData(data);
            }
        } catch (error) {
            console.error('Dashboard refresh failed:', error);
        }
    }

    /**
     * Update dashboard data
     */
    updateDashboardData(data) {
        // Update verification status
        if (data.verification) {
            Object.keys(data.verification).forEach(key => {
                const element = document.querySelector(`[data-verification="${key}"]`);
                if (element) {
                    const isVerified = data.verification[key];
                    element.className = `w-8 h-8 rounded-full flex items-center justify-center ${
                        isVerified ? 'bg-cyber-green' : 'bg-cyber-red'
                    }`;
                }
            });
        }
        
        // Update accounting data
        if (data.accounting) {
            const elements = {
                '.mining-rewards': data.accounting.mining_rewards,
                '.mikvah-tokens': data.accounting.mikvah_tokens,
                '.onx-balance': data.accounting.onx_balance
            };
            
            Object.keys(elements).forEach(selector => {
                const element = document.querySelector(selector);
                if (element && elements[selector] !== undefined) {
                    element.textContent = elements[selector];
                }
            });
        }
    }
}

// Initialize dashboard when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.selaDashboard = new SelaDashboard();
});

// Export for manual access
window.SelaDashboard = SelaDashboard;
