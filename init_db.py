#!/usr/bin/env python3
"""
Database Initialization Script for ONNYX Backend

This script initializes the SQLite database with the required schema
for the ONNYX platform when deployed on Render or other cloud platforms.
"""

import os
import sqlite3
import sys
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def init_database():
    """Initialize the database with the required schema."""
    
    # Database path
    db_path = "shared/db/onnyx.db"
    
    # Ensure the directory exists
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    
    # Schema file path
    schema_path = "shared/schemas/production_schema.sql"
    
    if not os.path.exists(schema_path):
        # Fallback to regular schema
        schema_path = "shared/schemas/schema.sql"
        
    if not os.path.exists(schema_path):
        logger.error(f"Schema file not found: {schema_path}")
        return False
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Read and execute schema
        with open(schema_path, 'r') as f:
            schema_sql = f.read()
            
        # Execute schema (split by semicolon to handle multiple statements)
        statements = [stmt.strip() for stmt in schema_sql.split(';') if stmt.strip()]
        
        for statement in statements:
            try:
                cursor.execute(statement)
                logger.info(f"Executed: {statement[:50]}...")
            except sqlite3.Error as e:
                # Log but continue - some statements might fail if tables already exist
                logger.warning(f"Statement failed (continuing): {e}")
        
        conn.commit()
        conn.close()
        
        logger.info(f"Database initialized successfully at {db_path}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        return False

def check_tables():
    """Check if required tables exist."""
    db_path = "shared/db/onnyx.db"
    
    if not os.path.exists(db_path):
        logger.info("Database file does not exist")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check for key tables
        required_tables = ['identities', 'selas', 'transactions', 'blocks']
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        existing_tables = [row[0] for row in cursor.fetchall()]
        
        logger.info(f"Existing tables: {existing_tables}")
        
        missing_tables = [table for table in required_tables if table not in existing_tables]
        
        if missing_tables:
            logger.info(f"Missing tables: {missing_tables}")
            return False
        else:
            logger.info("All required tables exist")
            return True
            
        conn.close()
        
    except Exception as e:
        logger.error(f"Failed to check tables: {e}")
        return False

if __name__ == "__main__":
    logger.info("Starting database initialization...")
    
    # Check if tables already exist
    if check_tables():
        logger.info("Database already initialized")
        sys.exit(0)
    
    # Initialize database
    if init_database():
        logger.info("Database initialization completed successfully")
        sys.exit(0)
    else:
        logger.error("Database initialization failed")
        sys.exit(1)
