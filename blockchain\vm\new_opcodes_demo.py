"""
Onnyx VM New Opcodes Demo

This script demonstrates how to use the new opcodes in the Onnyx VM:
- OP_BURN: For burning tokens
- OP_GRANT_REPUTATION: For granting reputation to identities
- OP_STAKE: For staking tokens
- OP_VOTE: For voting on governance proposals
"""

import sys
import os
import json
import time
import hashlib
import uuid
from typing import Dict, List, Any

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Add the VM directory to the Python path
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "vm"))

from vm import OnnyxVM
from opcodes import (
    OP_MINT, OP_SEND, OP_IDENTITY, OP_SCROLL,
    OP_BURN, OP_GRANT_REPUTATION, OP_STAKE, OP_VOTE,
    identity, selas, tokens, ledger, scrolls
)

def setup_demo_data():
    """Set up demo data for the opcodes demo."""
    print("Setting up demo data...")

    # Set up identities
    identity.identities["alice"] = {
        "name": "Alice",
        "public_key": "0x123456789abcdef",
        "created_at": int(time.time())
    }

    identity.identities["bob"] = {
        "name": "Bob",
        "public_key": "0x987654321fedcba",
        "created_at": int(time.time())
    }

    identity.identities["charlie"] = {
        "name": "Charlie",
        "public_key": "0xabcdef123456789",
        "created_at": int(time.time())
    }

    # Set up badges
    identity.badges["alice"] = ["CREATOR", "VALIDATOR"]
    identity.badges["bob"] = ["CREATOR"]
    identity.badges["charlie"] = []

    # Set up reputation
    identity.reputation["alice"] = 50
    identity.reputation["bob"] = 20
    identity.reputation["charlie"] = 5

    # Set up Selas
    selas.selas["alice"] = {
        "name": "Alice's Business",
        "type": "service",
        "tier": "gold",
        "created_at": int(time.time())
    }

    # Set up tokens
    tokens.tokens["ALICE"] = {
        "symbol": "ALICE",
        "name": "Alice Token",
        "creator": "alice",
        "supply": 10000,
        "created_at": int(time.time())
    }

    tokens.tokens["ONX"] = {
        "symbol": "ONX",
        "name": "Onnyx",
        "creator": "alice",
        "supply": 1000000,
        "created_at": int(time.time())
    }

    # Set up ledger
    ledger.balances["alice:ALICE"] = 10000
    ledger.balances["alice:ONX"] = 50000
    ledger.balances["bob:ONX"] = 20000
    ledger.balances["charlie:ONX"] = 5000

    # Set up scrolls
    scrolls.scrolls["scroll1"] = {
        "id": "scroll1",
        "title": "Increase Yovel Mint Cap",
        "description": "Proposal to increase the Yovel mint cap from 1000 to 2000 tokens.",
        "category": "economic",
        "creator": "alice",
        "created_at": int(time.time())
    }

    print("Demo data setup complete.")

def create_transaction(op: str, from_id: str = None, data: Dict[str, Any] = None) -> Dict[str, Any]:
    """Create a transaction with the given parameters."""
    if data is None:
        data = {}

    # Map opcode to transaction type
    op_to_type = {
        OP_MINT: "mint",
        OP_SEND: "send",
        OP_IDENTITY: "identity",
        OP_SCROLL: "scroll",
        OP_BURN: "burn",
        OP_GRANT_REPUTATION: "grant_reputation",
        OP_STAKE: "stake",
        OP_VOTE: "vote"
    }

    tx = {
        "type": op_to_type.get(op, "unknown"),
        "op": op,  # Keep the op field for the validator
        "txid": hashlib.sha256(f"{op}-{from_id}-{uuid.uuid4()}".encode()).hexdigest()[:16],
        "data": data
    }

    if from_id:
        tx["from"] = from_id

    return tx

def demo_burn_token():
    """Demonstrate token burning."""
    print("\n=== Token Burning ===")

    vm = OnnyxVM()

    # Show initial token supply
    token = tokens.tokens["ALICE"]
    print(f"Initial {token['symbol']} token supply: {token['supply']}")
    print(f"Alice's balance: {ledger.balances['alice:ALICE']} {token['symbol']}")

    # Create a burn transaction
    burn_tx = create_transaction(OP_BURN, "alice", {
        "token_id": "ALICE",
        "amount": 1000
    })

    print(f"\nBurning {burn_tx['data']['amount']} {burn_tx['data']['token_id']} tokens...")

    # Execute the transaction
    result = vm.execute_transaction(burn_tx)

    print(f"Transaction status: {result['status']}")
    if result['status'] == "success":
        # Update token supply and balance
        tokens.tokens["ALICE"]["supply"] -= 1000
        ledger.balances["alice:ALICE"] -= 1000

        print(f"\nNew {token['symbol']} token supply: {tokens.tokens['ALICE']['supply']}")
        print(f"Alice's balance: {ledger.balances['alice:ALICE']} {token['symbol']}")
    else:
        print(f"Error: {result['message']}")

    return vm

def demo_grant_reputation():
    """Demonstrate reputation granting."""
    print("\n=== Reputation Granting ===")

    vm = OnnyxVM()

    # Show initial reputation scores
    print(f"Initial reputation scores:")
    print(f"Alice: {identity.reputation['alice']}")
    print(f"Bob: {identity.reputation['bob']}")
    print(f"Charlie: {identity.reputation['charlie']}")

    # Create a grant reputation transaction
    grant_tx = create_transaction(OP_GRANT_REPUTATION, "alice", {
        "to_identity": "charlie",
        "amount": 10
    })

    print(f"\nGranting {grant_tx['data']['amount']} reputation points to {grant_tx['data']['to_identity']}...")

    # Execute the transaction
    result = vm.execute_transaction(grant_tx)

    print(f"Transaction status: {result['status']}")
    if result['status'] == "success":
        # Update reputation score
        identity.reputation["charlie"] += 10

        print(f"\nNew reputation scores:")
        print(f"Alice: {identity.reputation['alice']}")
        print(f"Bob: {identity.reputation['bob']}")
        print(f"Charlie: {identity.reputation['charlie']}")
    else:
        print(f"Error: {result['message']}")

    return vm

def demo_stake_tokens():
    """Demonstrate token staking."""
    print("\n=== Token Staking ===")

    vm = OnnyxVM()

    # Show initial balances and stakes
    print(f"Initial balances:")
    print(f"Alice's ONX balance: {ledger.balances['alice:ONX']}")
    print(f"Alice's ONX stake: {ledger.get_staked_balance('alice', 'ONX')}")

    # Create a stake transaction
    stake_tx = create_transaction(OP_STAKE, "alice", {
        "token_id": "ONX",
        "amount": 10000,
        "duration": 30  # 30 days
    })

    print(f"\nStaking {stake_tx['data']['amount']} {stake_tx['data']['token_id']} tokens for {stake_tx['data']['duration']} days...")

    # Execute the transaction
    result = vm.execute_transaction(stake_tx)

    print(f"Transaction status: {result['status']}")
    if result['status'] == "success":
        # Update balances and stakes
        ledger.stake_tokens("alice", "ONX", 10000, 30)

        print(f"\nNew balances:")
        print(f"Alice's ONX balance: {ledger.balances['alice:ONX']}")
        print(f"Alice's ONX stake: {ledger.get_staked_balance('alice', 'ONX')}")

        # Check if Alice has earned any badges from staking
        if ledger.get_staked_balance("alice", "ONX") >= 10000:
            if "STAKER" not in identity.badges["alice"]:
                identity.badges["alice"].append("STAKER")
                print(f"\nAlice has earned the STAKER badge!")
    else:
        print(f"Error: {result['message']}")

    return vm

def demo_vote_on_scroll():
    """Demonstrate voting on a governance proposal."""
    print("\n=== Governance Voting ===")

    vm = OnnyxVM()

    # Show scroll details
    scroll = scrolls.scrolls["scroll1"]
    print(f"Scroll: {scroll['title']}")
    print(f"Description: {scroll['description']}")
    print(f"Category: {scroll['category']}")
    print(f"Creator: {scroll['creator']}")

    # Show initial votes
    print(f"\nInitial votes: {scrolls.get_votes('scroll1')}")

    # Create vote transactions for different identities
    alice_vote_tx = create_transaction(OP_VOTE, "alice", {
        "scroll_id": "scroll1",
        "vote": True
    })

    bob_vote_tx = create_transaction(OP_VOTE, "bob", {
        "scroll_id": "scroll1",
        "vote": True
    })

    # Charlie can't vote due to low reputation
    charlie_vote_tx = create_transaction(OP_VOTE, "charlie", {
        "scroll_id": "scroll1",
        "vote": False
    })

    # Execute Alice's vote
    print(f"\nAlice is voting {alice_vote_tx['data']['vote']} on {alice_vote_tx['data']['scroll_id']}...")
    result = vm.execute_transaction(alice_vote_tx)

    print(f"Transaction status: {result['status']}")
    if result['status'] == "success":
        # Record the vote
        scrolls.vote_on_scroll("scroll1", "alice", True)
    else:
        print(f"Error: {result['message']}")

    # Execute Bob's vote
    print(f"\nBob is voting {bob_vote_tx['data']['vote']} on {bob_vote_tx['data']['scroll_id']}...")
    result = vm.execute_transaction(bob_vote_tx)

    print(f"Transaction status: {result['status']}")
    if result['status'] == "success":
        # Record the vote
        scrolls.vote_on_scroll("scroll1", "bob", True)
    else:
        print(f"Error: {result['message']}")

    # Execute Charlie's vote (should fail)
    print(f"\nCharlie is voting {charlie_vote_tx['data']['vote']} on {charlie_vote_tx['data']['scroll_id']}...")
    result = vm.execute_transaction(charlie_vote_tx)

    print(f"Transaction status: {result['status']}")
    if result['status'] == "success":
        # Record the vote
        scrolls.vote_on_scroll("scroll1", "charlie", False)
    else:
        print(f"Error: {result['message']}")

    # Show final votes
    print(f"\nFinal votes: {scrolls.get_votes('scroll1')}")

    # Tally the votes
    votes = scrolls.get_votes("scroll1")
    yes_votes = sum(1 for vote in votes.values() if vote)
    no_votes = sum(1 for vote in votes.values() if not vote)

    print(f"\nVote tally:")
    print(f"Yes: {yes_votes}")
    print(f"No: {no_votes}")

    if yes_votes > no_votes:
        print(f"The scroll has been approved!")
    elif no_votes > yes_votes:
        print(f"The scroll has been rejected.")
    else:
        print(f"The vote is tied.")

    return vm

def main():
    """Run the new opcodes demo."""
    print("Onnyx VM New Opcodes Demo")
    print("=========================")

    # Set up demo data
    setup_demo_data()

    # Run demo scenarios
    demo_burn_token()
    demo_grant_reputation()
    demo_stake_tokens()
    demo_vote_on_scroll()

    print("\nDemo completed.")

if __name__ == "__main__":
    main()
