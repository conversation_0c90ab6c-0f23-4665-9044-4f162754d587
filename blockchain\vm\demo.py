"""
Onnyx VM Demo Script

This script demonstrates how to use the Onnyx VM to validate and execute
transactions on the Onnyx blockchain.
"""

import sys
import os
import json
import time
import uuid
import hashlib

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Add the VM directory to the Python path
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "vm"))

from vm import OnnyxVM
from opcodes import identity, selas, tokens, ledger

def setup_demo_data():
    """Set up demo data for the VM."""
    print("Setting up demo data...")

    # Set up identities
    identity.identities["alice"] = {
        "name": "Alice",
        "public_key": "0x123456789abcdef",
        "created_at": int(time.time())
    }

    identity.identities["bob"] = {
        "name": "<PERSON>",
        "public_key": "0x987654321fedcba",
        "created_at": int(time.time())
    }

    # Set up badges
    identity.badges["alice"] = ["CREATOR", "VALIDATOR"]
    identity.badges["bob"] = ["CREATOR"]

    # Set up Selas
    selas.selas["alice"] = {
        "name": "Alice's Business",
        "type": "service",
        "tier": "gold",
        "created_at": int(time.time())
    }

    # Set up tokens
    tokens.tokens["ALICE"] = {
        "symbol": "ALICE",
        "name": "Alice Token",
        "creator": "alice",
        "supply": 10000,
        "created_at": int(time.time())
    }

    # Set up ledger
    ledger.balances["alice:ALICE"] = 10000

    print("Demo data setup complete.")

def generate_txid():
    """Generate a unique transaction ID."""
    return hashlib.sha256(str(uuid.uuid4()).encode()).hexdigest()[:16]

def demo_identity_creation():
    """Demonstrate identity creation."""
    print("\n=== Identity Creation ===")

    vm = OnnyxVM()

    # Create a new identity transaction
    tx = {
        "type": "identity",
        "txid": generate_txid(),
        "data": {
            "identity_id": "charlie",
            "name": "Charlie",
            "public_key": "0xabcdef123456789"
        }
    }

    print(f"Creating identity: {tx['data']['name']} (ID: {tx['data']['identity_id']})")

    # Execute the transaction
    result = vm.execute_transaction(tx)

    print(f"Result: {result['status']}")
    print(f"Message: {result['message']}")

    # Check if the identity was created
    if result['status'] == "success":
        identity.identities["charlie"] = {
            "name": "Charlie",
            "public_key": "0xabcdef123456789",
            "created_at": int(time.time())
        }
        print("Identity created successfully.")

    return vm

def demo_token_minting(vm):
    """Demonstrate token minting."""
    print("\n=== Token Minting ===")

    # Register Charlie's Sela
    selas.selas["charlie"] = {
        "name": "Charlie's Business",
        "type": "product",
        "tier": "silver",
        "created_at": int(time.time())
    }

    # Add CREATOR badge to Charlie
    identity.badges["charlie"] = ["CREATOR"]

    # Create a token minting transaction
    tx = {
        "type": "mint",
        "txid": generate_txid(),
        "from": "charlie",
        "data": {
            "symbol": "CHARLIE",
            "name": "Charlie Token",
            "supply": 1000  # Adjusted to be within the Yovel cap
        }
    }

    print(f"Minting token: {tx['data']['name']} (Symbol: {tx['data']['symbol']})")
    print(f"Supply: {tx['data']['supply']}")
    print(f"Creator: {tx['from']}")

    # Execute the transaction
    result = vm.execute_transaction(tx)

    print(f"Result: {result['status']}")
    print(f"Message: {result['message']}")

    # Update token registry and ledger if successful
    if result['status'] == "success":
        tokens.tokens["CHARLIE"] = {
            "symbol": "CHARLIE",
            "name": "Charlie Token",
            "creator": "charlie",
            "supply": 1000,  # Adjusted to match the transaction
            "created_at": int(time.time())
        }
        ledger.balances["charlie:CHARLIE"] = 1000  # Adjusted to match the transaction
        print("Token minted successfully.")
    else:
        # For demo purposes, let's create the token anyway to make the transfer work
        print("For demo purposes, creating the token anyway...")
        tokens.tokens["CHARLIE"] = {
            "symbol": "CHARLIE",
            "name": "Charlie Token",
            "creator": "charlie",
            "supply": 1000,
            "created_at": int(time.time())
        }
        ledger.balances["charlie:CHARLIE"] = 1000

    return vm

def demo_token_transfer(vm):
    """Demonstrate token transfer."""
    print("\n=== Token Transfer ===")

    # Create a token transfer transaction
    tx = {
        "type": "send",
        "txid": generate_txid(),
        "from": "charlie",
        "data": {
            "token_id": "CHARLIE",
            "to": "alice",
            "amount": 1000
        }
    }

    print(f"Transferring {tx['data']['amount']} {tx['data']['token_id']} tokens")
    print(f"From: {tx['from']}")
    print(f"To: {tx['data']['to']}")

    # Execute the transaction
    result = vm.execute_transaction(tx)

    print(f"Result: {result['status']}")
    print(f"Message: {result['message']}")

    # Update ledger if successful
    if result['status'] == "success":
        ledger.balances["charlie:CHARLIE"] -= 1000
        ledger.balances["alice:CHARLIE"] = ledger.balances.get("alice:CHARLIE", 0) + 1000
        print("Token transfer successful.")
        print(f"Charlie's balance: {ledger.balances['charlie:CHARLIE']} CHARLIE")
        print(f"Alice's balance: {ledger.balances['alice:CHARLIE']} CHARLIE")

    return vm

def demo_governance_proposal(vm):
    """Demonstrate governance proposal."""
    print("\n=== Governance Proposal ===")

    # Create a governance proposal transaction
    tx = {
        "type": "scroll",
        "txid": generate_txid(),
        "from": "alice",
        "data": {
            "title": "Increase Yovel Mint Cap",
            "description": "Proposal to increase the Yovel mint cap from 1000 to 2000 tokens.",
            "category": "economic"
        }
    }

    print(f"Creating governance proposal: {tx['data']['title']}")
    print(f"Category: {tx['data']['category']}")
    print(f"Proposer: {tx['from']}")

    # Execute the transaction
    result = vm.execute_transaction(tx)

    print(f"Result: {result['status']}")
    print(f"Message: {result['message']}")

    # Record the proposal if successful
    if result['status'] == "success":
        print("Governance proposal created successfully.")

    return vm

def demo_transaction_history(vm):
    """Demonstrate transaction history."""
    print("\n=== Transaction History ===")

    history = vm.get_transaction_history()

    print(f"Total transactions: {len(history)}")

    for i, tx in enumerate(history):
        print(f"\nTransaction {i+1}:")
        print(f"  Type: {tx['type']}")
        print(f"  ID: {tx['txid']}")
        if 'from' in tx:
            print(f"  From: {tx['from']}")
        print(f"  Timestamp: {tx.get('timestamp', 'N/A')}")

        # Get the result
        result = vm.get_transaction_result(tx['txid'])
        if result:
            print(f"  Status: {result['status']}")

def main():
    """Run the Onnyx VM demo."""
    print("Onnyx VM Demo")
    print("=============")

    # Set up demo data
    setup_demo_data()

    # Run demo scenarios
    vm = demo_identity_creation()
    vm = demo_token_minting(vm)
    vm = demo_token_transfer(vm)
    vm = demo_governance_proposal(vm)

    # Show transaction history
    demo_transaction_history(vm)

    print("\nDemo completed.")

if __name__ == "__main__":
    main()
