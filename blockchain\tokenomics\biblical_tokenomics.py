"""
Biblical Tokenomics System
Implements covenant-based economic principles including Yovel (Jubilee),
Sabbath enforcement, Gleaning pools, and Anti-usury lending
"""

import time
import logging
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta

# Set up logging
logger = logging.getLogger("onnyx.tokenomics.biblical")

class BiblicalTokenomics:
    """
    Core biblical tokenomics system implementing covenant economic principles.
    """

    def __init__(self, db_connection):
        """Initialize the biblical tokenomics system."""
        self.db = db_connection

        # Biblical constants
        self.YOVEL_CYCLE_YEARS = 7  # 7-year jubilee cycle
        self.SEASON_DAYS = 90  # 90-day seasons (Zeman periods)
        self.SABBATH_DAY = 6  # Saturday (0=Monday, 6=Sunday)
        self.MAX_TOKENS_PER_YOVEL = 10000.0  # Maximum tokens per jubilee cycle
        self.GLEANING_PERCENTAGE = 0.1  # 10% of rewards go to gleaning pool
        self.SABBATH_BONUS_MULTIPLIER = 1.2  # 20% bonus for sabbath observers

        self.current_yovel_cycle = self._calculate_current_yovel_cycle()
        self.current_season = self._calculate_current_season()

        logger.info(f"Biblical Tokenomics initialized - Yovel Cycle: {self.current_yovel_cycle}, Season: {self.current_season}")

    def _calculate_current_yovel_cycle(self) -> int:
        """Calculate the current 7-year Yovel (Jubilee) cycle."""
        # Use Unix epoch as starting point, calculate 7-year cycles
        epoch_start = datetime(1970, 1, 1)
        now = datetime.now()
        years_since_epoch = (now - epoch_start).days / 365.25
        return int(years_since_epoch // self.YOVEL_CYCLE_YEARS)

    def _calculate_current_season(self) -> int:
        """Calculate the current Zeman (season) period."""
        # Calculate seasons based on 90-day periods
        epoch_start = datetime(1970, 1, 1)
        now = datetime.now()
        days_since_epoch = (now - epoch_start).days
        return int(days_since_epoch // self.SEASON_DAYS)

    def calculate_labor_reward(self, labor_record: Dict[str, Any]) -> Tuple[float, int]:
        """
        Calculate Mikvah token reward and Etzem points for verified labor.

        Args:
            labor_record: Dictionary containing labor details

        Returns:
            Tuple of (mikvah_tokens, etzem_points)
        """
        try:
            base_value = float(labor_record.get('value_estimate', 0.0))
            labor_type = labor_record.get('labor_type', 'service')
            identity_id = labor_record.get('identity_id')

            # Base calculation
            mikvah_tokens = base_value * 0.1  # 10% of estimated value as tokens
            etzem_points = max(1, int(base_value * 0.05))  # 5% as reputation points

            # Labor type multipliers
            type_multipliers = {
                'teaching': 1.5,      # Teaching gets 50% bonus
                'healing': 1.4,       # Healing gets 40% bonus
                'community': 1.3,     # Community service gets 30% bonus
                'governance': 1.2,    # Governance gets 20% bonus
                'farming': 1.1,       # Farming gets 10% bonus
                'service': 1.0,       # Standard service
                'product': 0.9        # Product creation gets 90%
            }

            multiplier = type_multipliers.get(labor_type, 1.0)
            mikvah_tokens *= multiplier
            etzem_points = int(etzem_points * multiplier)

            # Check sabbath compliance bonus
            if self._is_sabbath_observer(identity_id):
                mikvah_tokens *= self.SABBATH_BONUS_MULTIPLIER
                etzem_points = int(etzem_points * self.SABBATH_BONUS_MULTIPLIER)

            # Apply Yovel limits
            if not self.check_yovel_eligibility(identity_id, mikvah_tokens):
                logger.warning(f"Yovel limit reached for identity {identity_id}")
                mikvah_tokens = 0  # No tokens if limit exceeded
                etzem_points = max(1, etzem_points // 2)  # Half Etzem points

            return round(mikvah_tokens, 2), etzem_points

        except Exception as e:
            logger.error(f"Error calculating labor reward: {e}")
            return 0.0, 0

    def check_yovel_eligibility(self, identity_id: str, additional_tokens: float = 0.0) -> bool:
        """
        Check if identity is eligible for more tokens within Yovel cycle.

        Args:
            identity_id: Identity to check
            additional_tokens: Additional tokens being considered

        Returns:
            True if eligible, False if would exceed Yovel limit
        """
        try:
            # Get current cycle tokens
            current_tokens = self.db.query_one("""
                SELECT COALESCE(SUM(amount), 0) as total
                FROM mikvah_transactions
                WHERE identity_id = ? AND yovel_cycle = ? AND amount > 0
            """, (identity_id, self.current_yovel_cycle))

            total_tokens = current_tokens['total'] if current_tokens else 0.0

            return (total_tokens + additional_tokens) <= self.MAX_TOKENS_PER_YOVEL

        except Exception as e:
            logger.error(f"Error checking Yovel eligibility: {e}")
            return False

    def process_labor_mint(self, labor_id: str) -> Dict[str, Any]:
        """
        Process token minting for verified labor.

        Args:
            labor_id: ID of the labor record to process

        Returns:
            Dictionary with minting results
        """
        try:
            # Get labor record
            labor = self.db.query_one("""
                SELECT * FROM labor_records WHERE labor_id = ?
            """, (labor_id,))

            if not labor:
                return {'success': False, 'error': 'Labor record not found'}

            if labor['verification_status'] != 'verified':
                return {'success': False, 'error': 'Labor not verified'}

            # Check if already processed
            existing_transaction = self.db.query_one("""
                SELECT transaction_id FROM mikvah_transactions WHERE labor_id = ?
            """, (labor_id,))

            if existing_transaction:
                return {'success': False, 'error': 'Labor already processed'}

            # Calculate rewards
            mikvah_tokens, etzem_points = self.calculate_labor_reward(labor)

            if mikvah_tokens <= 0:
                return {'success': False, 'error': 'No tokens to mint'}

            # Get current balance
            current_balance = self._get_mikvah_balance(labor['identity_id'])
            new_balance = current_balance + mikvah_tokens

            # Create transaction ID
            transaction_id = f"LABOR_{labor_id}_{int(time.time())}"

            # Record Mikvah transaction
            self.db.execute("""
                INSERT INTO mikvah_transactions
                (transaction_id, identity_id, labor_id, transaction_type, amount,
                 balance_before, balance_after, season_period, yovel_cycle, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (transaction_id, labor['identity_id'], labor_id, 'labor_reward',
                  mikvah_tokens, current_balance, new_balance, self.current_season,
                  self.current_yovel_cycle, int(time.time())))

            # Update Etzem score
            self.update_etzem_score(labor['identity_id'], etzem_points)

            # Update labor record with points earned
            self.db.execute("""
                UPDATE labor_records SET etzem_points = ? WHERE labor_id = ?
            """, (etzem_points, labor_id))

            # Contribute to gleaning pool
            gleaning_contribution = mikvah_tokens * self.GLEANING_PERCENTAGE
            self._contribute_to_gleaning_pool(gleaning_contribution)

            logger.info(f"Labor mint processed: {mikvah_tokens} tokens, {etzem_points} Etzem for {labor['identity_id']}")

            return {
                'success': True,
                'mikvah_tokens': mikvah_tokens,
                'etzem_points': etzem_points,
                'new_balance': new_balance,
                'transaction_id': transaction_id,
                'gleaning_contribution': gleaning_contribution
            }

        except Exception as e:
            logger.error(f"Error processing labor mint: {e}")
            return {'success': False, 'error': str(e)}

    def update_etzem_score(self, identity_id: str, points_change: int) -> bool:
        """
        Update Etzem (reputation) score for an identity.

        Args:
            identity_id: Identity to update
            points_change: Points to add (positive) or subtract (negative)

        Returns:
            True if successful, False otherwise
        """
        try:
            # Get current score
            current_score = self.db.query_one("""
                SELECT etzem_score FROM identities WHERE identity_id = ?
            """, (identity_id,))

            if not current_score:
                return False

            old_score = current_score['etzem_score'] or 0
            new_score = max(0, old_score + points_change)  # Don't go below 0

            # Update identity
            self.db.execute("""
                UPDATE identities SET etzem_score = ? WHERE identity_id = ?
            """, (new_score, identity_id))

            # Record in history
            self.db.execute("""
                INSERT INTO etzem_history
                (identity_id, old_score, new_score, change_reason, change_amount, timestamp)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (identity_id, old_score, new_score, 'Labor contribution', points_change, int(time.time())))

            return True

        except Exception as e:
            logger.error(f"Error updating Etzem score: {e}")
            return False

    def _get_mikvah_balance(self, identity_id: str) -> float:
        """Get current Mikvah token balance for an identity."""
        try:
            result = self.db.query_one("""
                SELECT COALESCE(SUM(amount), 0) as balance
                FROM mikvah_transactions
                WHERE identity_id = ?
            """, (identity_id,))

            return float(result['balance']) if result else 0.0

        except Exception as e:
            logger.error(f"Error getting Mikvah balance: {e}")
            return 0.0

    def _is_sabbath_observer(self, identity_id: str) -> bool:
        """Check if identity is a sabbath observer."""
        try:
            result = self.db.query_one("""
                SELECT sabbath_observer FROM identities WHERE identity_id = ?
            """, (identity_id,))

            return bool(result['sabbath_observer']) if result else False

        except Exception as e:
            logger.error(f"Error checking sabbath observer status: {e}")
            return False

    def _contribute_to_gleaning_pool(self, amount: float) -> bool:
        """Contribute to the current season's gleaning pool."""
        try:
            # Get or create current season pool
            pool = self.db.query_one("""
                SELECT * FROM gleaning_pool WHERE season_period = ?
            """, (self.current_season,))

            if pool:
                # Update existing pool
                self.db.execute("""
                    UPDATE gleaning_pool SET
                        total_contributions = total_contributions + ?,
                        current_balance = current_balance + ?,
                        contributor_count = contributor_count + 1,
                        last_updated = ?
                    WHERE pool_id = ?
                """, (amount, amount, int(time.time()), pool['pool_id']))
            else:
                # Create new pool
                pool_id = f"POOL_{self.current_season}_{int(time.time())}"
                self.db.execute("""
                    INSERT INTO gleaning_pool
                    (pool_id, season_period, total_contributions, current_balance,
                     contributor_count, last_updated)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (pool_id, self.current_season, amount, amount, 1, int(time.time())))

            return True

        except Exception as e:
            logger.error(f"Error contributing to gleaning pool: {e}")
            return False

    def is_sabbath_period(self) -> bool:
        """Check if current time is during Sabbath period."""
        try:
            current_time = int(time.time())

            # Check for active sabbath periods
            sabbath = self.db.query_one("""
                SELECT * FROM sabbath_enforcement
                WHERE start_timestamp <= ? AND end_timestamp >= ? AND active = 1
            """, (current_time, current_time))

            return bool(sabbath)

        except Exception as e:
            logger.error(f"Error checking sabbath period: {e}")
            return False

    def get_labor_statistics(self, identity_id: str) -> Dict[str, Any]:
        """Get comprehensive labor statistics for an identity."""
        try:
            # Current season stats
            current_season_stats = self.db.query_one("""
                SELECT
                    COUNT(*) as labor_count,
                    COALESCE(SUM(value_estimate), 0) as total_value,
                    COALESCE(SUM(etzem_points), 0) as total_etzem,
                    COUNT(CASE WHEN verification_status = 'verified' THEN 1 END) as verified_count
                FROM labor_records
                WHERE identity_id = ? AND season_period = ?
            """, (identity_id, self.current_season))

            # All-time stats
            all_time_stats = self.db.query_one("""
                SELECT
                    COUNT(*) as total_labor_count,
                    COALESCE(SUM(value_estimate), 0) as total_lifetime_value,
                    COALESCE(SUM(etzem_points), 0) as total_lifetime_etzem
                FROM labor_records
                WHERE identity_id = ? AND verification_status = 'verified'
            """, (identity_id,))

            # Mikvah balance
            mikvah_balance = self._get_mikvah_balance(identity_id)

            # Current Yovel cycle tokens
            yovel_tokens = self.db.query_one("""
                SELECT COALESCE(SUM(amount), 0) as tokens
                FROM mikvah_transactions
                WHERE identity_id = ? AND yovel_cycle = ? AND amount > 0
            """, (identity_id, self.current_yovel_cycle))

            return {
                'current_season': {
                    'labor_count': current_season_stats['labor_count'] if current_season_stats else 0,
                    'total_value': current_season_stats['total_value'] if current_season_stats else 0,
                    'total_etzem': current_season_stats['total_etzem'] if current_season_stats else 0,
                    'verified_count': current_season_stats['verified_count'] if current_season_stats else 0,
                    'verification_rate': (current_season_stats['verified_count'] / max(1, current_season_stats['labor_count'])) * 100 if current_season_stats else 0
                },
                'all_time': {
                    'total_labor_count': all_time_stats['total_labor_count'] if all_time_stats else 0,
                    'total_lifetime_value': all_time_stats['total_lifetime_value'] if all_time_stats else 0,
                    'total_lifetime_etzem': all_time_stats['total_lifetime_etzem'] if all_time_stats else 0
                },
                'tokens': {
                    'mikvah_balance': mikvah_balance,
                    'yovel_tokens': yovel_tokens['tokens'] if yovel_tokens else 0,
                    'yovel_limit': self.MAX_TOKENS_PER_YOVEL,
                    'yovel_remaining': self.MAX_TOKENS_PER_YOVEL - (yovel_tokens['tokens'] if yovel_tokens else 0)
                },
                'cycles': {
                    'current_yovel_cycle': self.current_yovel_cycle,
                    'current_season': self.current_season
                }
            }

        except Exception as e:
            logger.error(f"Error getting labor statistics: {e}")
            return {}
