#!/usr/bin/env python3
"""
Test Labor System
Comprehensive testing of the Covenant Labor System and Biblical Tokenomics
"""

import requests
import json
import time
import sys
import os

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

BASE_URL = "http://127.0.0.1:5000"

def test_labor_types_api():
    """Test the labor types API."""
    print("🔧 Testing Labor Types API...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/labor/types")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Labor Types API: {len(data['labor_types'])} types available")
            
            # Show labor types
            for labor_type, config in data['labor_types'].items():
                print(f"  {labor_type}: {config['multiplier']}x multiplier")
        else:
            print(f"❌ Labor Types API failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Labor Types API error: {e}")

def test_labor_dashboard():
    """Test the labor dashboard page."""
    print("🌐 Testing Labor Dashboard...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/labor/dashboard")
        if response.status_code == 200:
            print("✅ Labor Dashboard: Page loads successfully")
            
            # Check for labor-specific content
            content = response.text.lower()
            if "labor activity" in content and "mikvah tokens" in content:
                print("  ✅ Contains labor system content")
            else:
                print("  ⚠️ May not contain expected labor content")
        else:
            print(f"❌ Labor Dashboard: Failed to load ({response.status_code})")
            
    except Exception as e:
        print(f"❌ Labor Dashboard: Error loading page - {e}")

def test_biblical_tokenomics_integration():
    """Test biblical tokenomics integration."""
    print("🪙 Testing Biblical Tokenomics Integration...")
    
    try:
        from shared.db.db import db
        from blockchain.tokenomics.biblical_tokenomics import BiblicalTokenomics
        from blockchain.tokenomics.mikvah_tokens import MikvahTokenManager
        
        # Initialize systems
        biblical_tokenomics = BiblicalTokenomics(db)
        mikvah_manager = MikvahTokenManager(db)
        
        print(f"✅ Biblical Tokenomics: Yovel Cycle {biblical_tokenomics.current_yovel_cycle}")
        print(f"✅ Biblical Tokenomics: Season {biblical_tokenomics.current_season}")
        
        # Test Yovel eligibility check
        test_identity = "test_identity_123"
        eligible = biblical_tokenomics.check_yovel_eligibility(test_identity, 100.0)
        print(f"✅ Yovel Eligibility Check: {eligible}")
        
        # Test token balance
        balance = mikvah_manager.get_token_balance(test_identity)
        print(f"✅ Token Balance System: {balance}")
        
        # Test gleaning pool status
        pool_status = mikvah_manager.get_gleaning_pool_status()
        print(f"✅ Gleaning Pool: Season {pool_status.get('season_period', 'N/A')}")
        
    except Exception as e:
        print(f"❌ Biblical Tokenomics Integration error: {e}")

def test_database_tables():
    """Test that all labor system tables exist."""
    print("🗄️ Testing Database Tables...")
    
    try:
        from shared.db.db import db
        
        tables = [
            'labor_records',
            'labor_verifications', 
            'mikvah_transactions',
            'seasonal_labor_summaries',
            'gleaning_pool',
            'gleaning_distributions',
            'sabbath_enforcement'
        ]
        
        for table in tables:
            try:
                result = db.query_one(f"SELECT COUNT(*) as count FROM {table}")
                count = result['count'] if result else 0
                print(f"✅ {table}: {count} records")
            except Exception as e:
                print(f"❌ {table}: {e}")
                
    except Exception as e:
        print(f"❌ Database Tables error: {e}")

def test_labor_reward_calculations():
    """Test labor reward calculations."""
    print("💰 Testing Labor Reward Calculations...")
    
    try:
        from blockchain.tokenomics.labor_rewards import LaborRewardCalculator
        from shared.db.db import db
        
        calculator = LaborRewardCalculator(db)
        
        # Test different labor types
        test_labors = [
            {
                'identity_id': 'test_identity',
                'labor_type': 'teaching',
                'description': 'Teaching blockchain development',
                'value_estimate': 100.0
            },
            {
                'identity_id': 'test_identity',
                'labor_type': 'healing',
                'description': 'Providing medical consultation',
                'value_estimate': 150.0
            },
            {
                'identity_id': 'test_identity',
                'labor_type': 'community',
                'description': 'Organizing community event',
                'value_estimate': 75.0
            }
        ]
        
        for labor in test_labors:
            tokens, etzem = calculator.calculate_base_reward(labor)
            print(f"✅ {labor['labor_type']}: {tokens} tokens, {etzem} Etzem points")
            
            # Test validation
            is_valid, message = calculator.validate_labor_submission(labor)
            print(f"  Validation: {'✅' if is_valid else '❌'} {message}")
            
    except Exception as e:
        print(f"❌ Labor Reward Calculations error: {e}")

def test_sabbath_enforcement():
    """Test sabbath enforcement system."""
    print("🕯️ Testing Sabbath Enforcement...")
    
    try:
        from blockchain.tokenomics.biblical_tokenomics import BiblicalTokenomics
        from shared.db.db import db
        
        biblical_tokenomics = BiblicalTokenomics(db)
        
        # Check if currently in sabbath period
        is_sabbath = biblical_tokenomics.is_sabbath_period()
        print(f"✅ Sabbath Check: {'Currently in Sabbath' if is_sabbath else 'Not in Sabbath period'}")
        
        # Check sabbath periods in database
        sabbath_periods = db.query("SELECT * FROM sabbath_enforcement WHERE active = 1")
        print(f"✅ Active Sabbath Periods: {len(sabbath_periods)}")
        
        for period in sabbath_periods:
            print(f"  {period['period_type']}: {period['start_timestamp']} - {period['end_timestamp']}")
            
    except Exception as e:
        print(f"❌ Sabbath Enforcement error: {e}")

def test_gleaning_pool_system():
    """Test gleaning pool system."""
    print("🌾 Testing Gleaning Pool System...")
    
    try:
        from blockchain.tokenomics.mikvah_tokens import MikvahTokenManager
        from shared.db.db import db
        
        mikvah_manager = MikvahTokenManager(db)
        
        # Get current pool status
        pool_status = mikvah_manager.get_gleaning_pool_status()
        print(f"✅ Current Pool Balance: {pool_status.get('current_balance', 0.0)}")
        print(f"✅ Total Contributors: {pool_status.get('contributor_count', 0)}")
        print(f"✅ Total Recipients: {pool_status.get('recipient_count', 0)}")
        
        # Test contribution mechanism
        contribution_result = mikvah_manager._contribute_to_gleaning_pool(10.0, pool_status.get('season_period', 0))
        print(f"✅ Test Contribution: {'Success' if contribution_result else 'Failed'}")
        
    except Exception as e:
        print(f"❌ Gleaning Pool System error: {e}")

def test_yovel_cycle_system():
    """Test Yovel (jubilee) cycle system."""
    print("📅 Testing Yovel Cycle System...")
    
    try:
        from blockchain.tokenomics.mikvah_tokens import MikvahTokenManager
        from shared.db.db import db
        
        mikvah_manager = MikvahTokenManager(db)
        
        # Get current Yovel cycle summary
        yovel_summary = mikvah_manager.get_yovel_cycle_summary()
        print(f"✅ Current Yovel Cycle: {yovel_summary.get('yovel_cycle', 'N/A')}")
        print(f"✅ Total Distributed: {yovel_summary.get('total_distributed', 0.0)} tokens")
        print(f"✅ Participants: {yovel_summary.get('participant_count', 0)}")
        print(f"✅ Cycle Years: {yovel_summary.get('cycle_start_year', 'N/A')} - {yovel_summary.get('cycle_end_year', 'N/A')}")
        
    except Exception as e:
        print(f"❌ Yovel Cycle System error: {e}")

def test_web_pages():
    """Test labor-related web pages."""
    print("🌐 Testing Labor Web Pages...")
    
    pages = [
        ("/api/labor/dashboard", "Labor Dashboard"),
        ("/api/labor/types", "Labor Types API")
    ]
    
    for path, name in pages:
        try:
            response = requests.get(f"{BASE_URL}{path}")
            if response.status_code == 200:
                print(f"✅ {name}: Page loads successfully")
            else:
                print(f"❌ {name}: Failed to load ({response.status_code})")
                
        except Exception as e:
            print(f"❌ {name}: Error loading page - {e}")

def main():
    """Main test function."""
    print("🪙 Starting Covenant Labor System Tests...")
    print("=" * 60)
    
    # Wait for server to be ready
    print("⏳ Waiting for server to be ready...")
    time.sleep(2)
    
    # Test API endpoints
    test_labor_types_api()
    print()
    
    # Test web pages
    test_labor_dashboard()
    print()
    
    # Test database tables
    test_database_tables()
    print()
    
    # Test biblical tokenomics integration
    test_biblical_tokenomics_integration()
    print()
    
    # Test labor reward calculations
    test_labor_reward_calculations()
    print()
    
    # Test sabbath enforcement
    test_sabbath_enforcement()
    print()
    
    # Test gleaning pool system
    test_gleaning_pool_system()
    print()
    
    # Test Yovel cycle system
    test_yovel_cycle_system()
    print()
    
    # Test web pages
    test_web_pages()
    print()
    
    print("=" * 60)
    print("🎉 Covenant Labor System Testing Complete!")

if __name__ == '__main__':
    main()
