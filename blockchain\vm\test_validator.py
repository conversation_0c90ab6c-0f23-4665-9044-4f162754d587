"""
Test script for the Onnyx VM validator.

This script tests the validator module for routing transactions to the
appropriate validation functions.
"""

import sys
import os
import json

# Add the parent directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Add the VM directory to the Python path
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "vm"))

from validator import (
    validate_transaction,
    validate_transaction_safe,
    validate_transactions,
    ValidationError,
    format_validation_error,
    get_transaction_type,
    summarize_transaction
)

from opcodes import (
    OP_MINT, OP_SEND, OP_IDENTITY, OP_SCROLL,
    identity, selas, tokens, ledger
)

def setup_test_data():
    """Set up test data for the validator tests."""
    # Set up identities
    identity.identities["id1"] = {"name": "Test Identity 1"}
    identity.identities["id2"] = {"name": "Test Identity 2"}
    identity.badges["id1"] = ["CREATOR"]
    
    # Set up Selas
    selas.selas["id1"] = {"name": "Test Sela 1"}
    
    # Set up tokens
    tokens.tokens["TKN"] = {"symbol": "TKN", "supply": 1000}
    
    # Set up ledger
    ledger.balances["id1:TKN"] = 500

def test_validate_transaction():
    """Test the validate_transaction function."""
    print("\nTesting validate_transaction...")
    
    # Test valid send transaction
    valid_tx = {
        "op": OP_SEND,
        "from": "id1",
        "data": {
            "token_id": "TKN",
            "to": "id2",
            "amount": 100
        }
    }
    
    try:
        result = validate_transaction(valid_tx)
        print(f"Valid transaction: {result}")
    except ValidationError as e:
        print(f"Error: {e}")
    
    # Test invalid transaction (missing op)
    invalid_tx = {
        "from": "id1",
        "data": {
            "token_id": "TKN",
            "to": "id2",
            "amount": 100
        }
    }
    
    try:
        result = validate_transaction(invalid_tx)
        print(f"Invalid transaction (should not reach here): {result}")
    except ValidationError as e:
        print(f"Expected error: {e}")
    
    # Test invalid transaction (unknown op)
    invalid_tx2 = {
        "op": "OP_UNKNOWN",
        "from": "id1",
        "data": {
            "token_id": "TKN",
            "to": "id2",
            "amount": 100
        }
    }
    
    try:
        result = validate_transaction(invalid_tx2)
        print(f"Invalid transaction (should not reach here): {result}")
    except ValidationError as e:
        print(f"Expected error: {e}")

def test_validate_transaction_safe():
    """Test the validate_transaction_safe function."""
    print("\nTesting validate_transaction_safe...")
    
    # Test valid send transaction
    valid_tx = {
        "op": OP_SEND,
        "from": "id1",
        "data": {
            "token_id": "TKN",
            "to": "id2",
            "amount": 100
        }
    }
    
    is_valid, error = validate_transaction_safe(valid_tx)
    print(f"Valid transaction: {is_valid}")
    if error:
        print(f"Error: {error}")
    
    # Test invalid transaction (missing op)
    invalid_tx = {
        "from": "id1",
        "data": {
            "token_id": "TKN",
            "to": "id2",
            "amount": 100
        }
    }
    
    is_valid, error = validate_transaction_safe(invalid_tx)
    print(f"Invalid transaction: {is_valid}")
    if error:
        print(f"Expected error: {error}")
    
    # Test invalid transaction (unknown op)
    invalid_tx2 = {
        "op": "OP_UNKNOWN",
        "from": "id1",
        "data": {
            "token_id": "TKN",
            "to": "id2",
            "amount": 100
        }
    }
    
    is_valid, error = validate_transaction_safe(invalid_tx2)
    print(f"Invalid transaction: {is_valid}")
    if error:
        print(f"Expected error: {error}")

def test_validate_transactions():
    """Test the validate_transactions function."""
    print("\nTesting validate_transactions...")
    
    # Create a list of transactions
    txs = [
        # Valid send transaction
        {
            "op": OP_SEND,
            "from": "id1",
            "data": {
                "token_id": "TKN",
                "to": "id2",
                "amount": 100
            }
        },
        # Invalid transaction (missing op)
        {
            "from": "id1",
            "data": {
                "token_id": "TKN",
                "to": "id2",
                "amount": 100
            }
        },
        # Invalid transaction (unknown op)
        {
            "op": "OP_UNKNOWN",
            "from": "id1",
            "data": {
                "token_id": "TKN",
                "to": "id2",
                "amount": 100
            }
        },
        # Valid identity transaction
        {
            "op": OP_IDENTITY,
            "data": {
                "identity_id": "id3",
                "name": "Test Identity 3",
                "public_key": "0x123456789abcdef"
            }
        }
    ]
    
    valid_txs, invalid_txs = validate_transactions(txs)
    
    print(f"Valid transactions: {len(valid_txs)}")
    print(f"Invalid transactions: {len(invalid_txs)}")
    
    for i, tx in enumerate(valid_txs):
        print(f"\nValid transaction {i+1}:")
        print(f"  Op: {tx['op']}")
    
    for i, result in enumerate(invalid_txs):
        print(f"\nInvalid transaction {i+1}:")
        print(f"  Error: {result['error']}")

def test_format_validation_error():
    """Test the format_validation_error function."""
    print("\nTesting format_validation_error...")
    
    # Test different error messages
    errors = [
        "Transaction missing 'op' field",
        "Unknown opcode: OP_UNKNOWN",
        "MINT denied: sender is not a registered Sela",
        "SEND denied: insufficient balance"
    ]
    
    for error in errors:
        formatted = format_validation_error(error)
        print(f"\nError: {error}")
        print(f"Formatted: {json.dumps(formatted, indent=2)}")

def test_transaction_summary():
    """Test the transaction summary functions."""
    print("\nTesting transaction summary functions...")
    
    # Test different transaction types
    txs = [
        {
            "op": OP_MINT,
            "from": "id1",
            "data": {
                "symbol": "TKN2",
                "supply": 1000
            }
        },
        {
            "op": OP_SEND,
            "from": "id1",
            "data": {
                "token_id": "TKN",
                "to": "id2",
                "amount": 100
            }
        },
        {
            "op": OP_IDENTITY,
            "data": {
                "identity_id": "id3",
                "name": "Test Identity 3",
                "public_key": "0x123456789abcdef"
            }
        },
        {
            "op": OP_SCROLL,
            "from": "id1",
            "data": {
                "title": "Test Scroll",
                "description": "This is a test scroll",
                "category": "test"
            }
        }
    ]
    
    for tx in txs:
        tx_type = get_transaction_type(tx)
        summary = summarize_transaction(tx)
        
        print(f"\nTransaction type: {tx_type}")
        print(f"Summary: {json.dumps(summary, indent=2)}")

def main():
    """Run all tests."""
    print("Testing Onnyx VM Validator")
    print("==========================")
    
    # Set up test data
    setup_test_data()
    
    # Run tests
    test_validate_transaction()
    test_validate_transaction_safe()
    test_validate_transactions()
    test_format_validation_error()
    test_transaction_summary()
    
    print("\nAll tests completed.")

if __name__ == "__main__":
    main()
