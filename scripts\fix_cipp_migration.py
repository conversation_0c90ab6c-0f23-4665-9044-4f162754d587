#!/usr/bin/env python3
"""
Fix CIPP Migration Script
Simple approach to add CIPP columns and migrate data
"""

import os
import sys
import time
import sqlite3

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

def get_db_path():
    """Get the database path."""
    # Use the same path as the application
    return os.path.join(os.path.dirname(__file__), '..', 'shared', 'db', 'onnyx.db')

def add_cipp_columns():
    """Add CIPP columns using direct SQLite connection."""
    db_path = get_db_path()

    if not os.path.exists(db_path):
        print(f"❌ Database not found at: {db_path}")
        return False

    try:
        print("🛡️ Connecting to database...")
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Check current table structure
        print("📊 Checking current identities table structure...")
        cursor.execute("PRAGMA table_info(identities)")
        columns = cursor.fetchall()
        existing_columns = [col[1] for col in columns]

        print(f"Current columns: {existing_columns}")

        # CIPP columns to add
        cipp_columns = [
            ("nation_of_origin", "TEXT DEFAULT 'JU'"),
            ("role_class", "TEXT DEFAULT 'Citizen'"),
            ("etzem_score", "INTEGER DEFAULT 10"),
            ("zeman_count", "INTEGER DEFAULT 0"),
            ("protection_tier", "TEXT DEFAULT 'Basic'"),
            ("verification_level", "INTEGER DEFAULT 0"),
            ("covenant_accepted", "BOOLEAN DEFAULT 1"),
            ("vault_status", "TEXT DEFAULT 'Active'"),
            ("last_activity_season", "INTEGER DEFAULT 0"),
            ("nation_code", "TEXT DEFAULT 'JU'"),
            ("nation_name", "TEXT DEFAULT 'Judah'")
        ]

        # Add columns that don't exist
        for column_name, column_def in cipp_columns:
            if column_name not in existing_columns:
                try:
                    alter_sql = f"ALTER TABLE identities ADD COLUMN {column_name} {column_def}"
                    print(f"Adding column: {column_name}")
                    cursor.execute(alter_sql)
                    print(f"✅ Added column: {column_name}")
                except Exception as e:
                    print(f"❌ Failed to add column {column_name}: {e}")
                    return False
            else:
                print(f"⏭️ Column already exists: {column_name}")

        # Commit changes
        conn.commit()

        # Update existing identities with CIPP defaults
        print("🔄 Updating existing identities...")
        current_time = int(time.time())

        update_sql = """
            UPDATE identities SET
                nation_of_origin = COALESCE(nation_of_origin, 'JU'),
                nation_code = COALESCE(nation_code, 'JU'),
                nation_name = COALESCE(nation_name, 'Judah'),
                role_class = COALESCE(role_class, 'Citizen'),
                etzem_score = COALESCE(etzem_score, 10),
                zeman_count = COALESCE(zeman_count, 0),
                protection_tier = COALESCE(protection_tier, 'Basic'),
                verification_level = COALESCE(verification_level, 0),
                covenant_accepted = COALESCE(covenant_accepted, 1),
                vault_status = COALESCE(vault_status, 'Active'),
                last_activity_season = COALESCE(last_activity_season, ?)
            WHERE nation_of_origin IS NULL OR nation_of_origin = ''
        """

        cursor.execute(update_sql, (current_time,))
        updated_rows = cursor.rowcount
        print(f"✅ Updated {updated_rows} identities with CIPP defaults")

        # Commit updates
        conn.commit()

        # Close connection
        conn.close()

        print("🎉 CIPP columns added and data migrated successfully!")
        return True

    except Exception as e:
        print(f"❌ Failed to add CIPP columns: {e}")
        return False

def create_cipp_tracking_records():
    """Create CIPP tracking records for existing identities."""
    try:
        from shared.db.db import db

        print("📝 Creating CIPP tracking records...")

        # Get all identities
        identities = db.query("SELECT identity_id, name FROM identities")
        current_time = int(time.time())

        for identity in identities:
            identity_id = identity['identity_id']

            try:
                # Create verification progress record
                existing_progress = db.query_one("""
                    SELECT identity_id FROM verification_progress WHERE identity_id = ?
                """, (identity_id,))

                if not existing_progress:
                    db.execute("""
                        INSERT INTO verification_progress
                        (identity_id, tier_0_completed, last_updated)
                        VALUES (?, ?, ?)
                    """, (identity_id, True, current_time))

                # Create covenant acceptance record
                existing_covenant = db.query_one("""
                    SELECT identity_id FROM covenant_acceptances WHERE identity_id = ?
                """, (identity_id,))

                if not existing_covenant:
                    db.execute("""
                        INSERT INTO covenant_acceptances
                        (identity_id, scroll_version, accepted_at, ip_address, user_agent)
                        VALUES (?, ?, ?, ?, ?)
                    """, (identity_id, 'v1.0', current_time, 'migration_script', 'CIPP_Migration_v1.0'))

                # Create initial Etzem score history
                existing_etzem = db.query_one("""
                    SELECT identity_id FROM etzem_history WHERE identity_id = ?
                """, (identity_id,))

                if not existing_etzem:
                    db.execute("""
                        INSERT INTO etzem_history
                        (identity_id, old_score, new_score, change_reason, change_amount, timestamp)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (identity_id, 0, 10, 'CIPP migration bonus', 10, current_time))

                # Create initial Zeman activity period
                existing_zeman = db.query_one("""
                    SELECT identity_id FROM zeman_activities WHERE identity_id = ?
                """, (identity_id,))

                if not existing_zeman:
                    db.execute("""
                        INSERT INTO zeman_activities
                        (identity_id, season_start, season_end, activity_count)
                        VALUES (?, ?, ?, ?)
                    """, (identity_id, current_time, current_time + (90 * 24 * 3600), 1))

                print(f"✅ Created tracking records for: {identity['name']}")

            except Exception as e:
                print(f"❌ Failed to create tracking records for {identity_id}: {e}")
                continue

        print(f"🎉 Created tracking records for {len(identities)} identities")
        return True

    except Exception as e:
        print(f"❌ Failed to create tracking records: {e}")
        return False

def main():
    """Main function."""
    print("🛡️ Starting CIPP Migration Fix...")

    # Add CIPP columns
    if not add_cipp_columns():
        return False

    # Create tracking records
    if not create_cipp_tracking_records():
        return False

    print("🎉 CIPP Migration Fix completed successfully!")
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
