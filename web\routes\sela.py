"""
Sela Routes

Public Sela profiles and business directory.
"""

import os
import sys
import json
import time
import logging
from flask import Blueprint, render_template, request, jsonify, session

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from shared.models.identity import Identity
from shared.db.db import db

logger = logging.getLogger("onnyx.web.sela")

sela_bp = Blueprint('sela', __name__)

# Helper functions for Sela dashboard
def get_sela_verification_status(sela_id, identity_id):
    """Get comprehensive verification status for a Sela."""
    try:
        # Check identity verification
        identity_verified = db.query_one("""
            SELECT COUNT(*) as count FROM identities
            WHERE identity_id = ? AND status = 'verified'
        """, (identity_id,))['count'] > 0

        # Check nation affiliation
        nation_verified = db.query_one("""
            SELECT COUNT(*) as count FROM identities
            WHERE identity_id = ? AND nation_code IS NOT NULL
        """, (identity_id,))['count'] > 0

        # Check recent activity (30 days)
        recent_transactions = db.query_one("""
            SELECT COUNT(*) as count FROM transactions
            WHERE (sender = ? OR data LIKE ?)
            AND created_at > ?
        """, (identity_id, f'%{sela_id}%', int(time.time()) - (30 * 24 * 3600)))['count']

        activity_verified = recent_transactions >= 3

        # Check Yovel compliance (simplified)
        yovel_compliant = True  # Placeholder for biblical jubilee compliance

        # Check Etzem growth
        etzem_score = db.query_one("""
            SELECT composite_score FROM etzem_scores
            WHERE identity_id = ?
        """, (identity_id,))

        etzem_growing = etzem_score and etzem_score['composite_score'] > 5.0 if etzem_score else False

        # Calculate overall score
        verifications = [identity_verified, nation_verified, activity_verified, yovel_compliant, etzem_growing]
        overall_score = (sum(verifications) / len(verifications)) * 100

        return {
            'identity_verified': identity_verified,
            'identity_failed': False,
            'nation_verified': nation_verified,
            'nation_failed': False,
            'activity_verified': activity_verified,
            'activity_failed': False,
            'recent_transactions': recent_transactions,
            'yovel_compliant': yovel_compliant,
            'yovel_failed': False,
            'etzem_growing': etzem_growing,
            'etzem_declining': False,
            'overall_score': overall_score
        }
    except Exception as e:
        logger.error(f"Error getting verification status: {e}")
        return {
            'identity_verified': False, 'identity_failed': True,
            'nation_verified': False, 'nation_failed': True,
            'activity_verified': False, 'activity_failed': True,
            'recent_transactions': 0,
            'yovel_compliant': False, 'yovel_failed': True,
            'etzem_growing': False, 'etzem_declining': True,
            'overall_score': 0
        }

def get_sela_accounting(sela_id, identity_id):
    """Get on-chain accounting information for a Sela."""
    try:
        # Get vault address (simplified)
        vault_address = f"0x{sela_id[:40]}"

        # Get mining rewards
        mining_rewards = db.query_one("""
            SELECT COALESCE(mining_rewards_earned, 0) as rewards
            FROM selas WHERE sela_id = ?
        """, (sela_id,))['rewards']

        # Get mikvah tokens (biblical tokenomics rewards)
        mikvah_tokens = db.query_one("""
            SELECT COALESCE(SUM(balance), 0) as tokens
            FROM token_balances tb
            JOIN tokens t ON tb.token_id = t.token_id
            WHERE tb.identity_id = ? AND t.category = 'mikvah'
        """, (identity_id,))

        mikvah_balance = mikvah_tokens['tokens'] if mikvah_tokens else 0

        return {
            'tax_id': f"ONX-{sela_id[:8].upper()}",
            'vault_address': vault_address,
            'mining_rewards': mining_rewards,
            'mikvah_tokens': mikvah_balance
        }
    except Exception as e:
        logger.error(f"Error getting accounting data: {e}")
        return {
            'tax_id': 'Pending',
            'vault_address': '0x0000000000000000000000000000000000000000',
            'mining_rewards': 0,
            'mikvah_tokens': 0
        }

def get_sela_participants(sela_id):
    """Get participants and structure for a Sela."""
    try:
        # Get founders (simplified - using identity_id from selas table)
        sela_info = db.query_one("SELECT identity_id FROM selas WHERE sela_id = ?", (sela_id,))

        if not sela_info:
            return {'founders': [], 'members': []}

        founder = db.query_one("""
            SELECT i.*, 'Founder' as role, s.created_at as joined_date
            FROM identities i
            JOIN selas s ON i.identity_id = s.identity_id
            WHERE s.sela_id = ?
        """, (sela_id,))

        founders = []
        if founder:
            founders.append({
                'name': founder['name'],
                'role': 'Founder & CEO',
                'nation_code': founder.get('nation_code', 'us'),
                'nation_name': founder.get('nation_name', 'United States'),
                'contribution_count': 15,  # Placeholder
                'etzem_score': 8.5,  # Placeholder
                'verified': True,
                'joined_date': founder['joined_date']
            })

        # Placeholder members data
        members = [
            {
                'name': 'Sarah Johnson',
                'role': 'Operations Manager',
                'nation_code': 'ca',
                'nation_name': 'Canada',
                'contribution_count': 12,
                'etzem_score': 7.8,
                'verified': True,
                'joined_date': int(time.time()) - (60 * 24 * 3600)
            },
            {
                'name': 'David Chen',
                'role': 'Technical Lead',
                'nation_code': 'sg',
                'nation_name': 'Singapore',
                'contribution_count': 18,
                'etzem_score': 9.1,
                'verified': True,
                'joined_date': int(time.time()) - (90 * 24 * 3600)
            }
        ]

        return {
            'founders': founders,
            'members': members
        }
    except Exception as e:
        logger.error(f"Error getting participants: {e}")
        return {'founders': [], 'members': []}

def get_sela_service_logs(sela_id):
    """Get service logs for a Sela."""
    try:
        # Get recent transactions as service logs
        logs = db.query("""
            SELECT * FROM transactions
            WHERE (sender = ? OR data LIKE ?)
            ORDER BY created_at DESC
            LIMIT 20
        """, (sela_id, f'%{sela_id}%'))

        service_logs = []
        for log in logs:
            service_logs.append({
                'service_type': 'Blockchain Validation',
                'description': f'Transaction {log["transaction_id"][:12]}...',
                'amount': 2.5,  # Placeholder
                'timestamp': log['created_at']
            })

        return service_logs
    except Exception as e:
        logger.error(f"Error getting service logs: {e}")
        return []

def get_sub_selas(sela_id):
    """Get sub-selas for a Sela."""
    try:
        # Placeholder for sub-selas (future feature)
        return []
    except Exception as e:
        logger.error(f"Error getting sub-selas: {e}")
        return []

def get_sela_governance(sela_id):
    """Get governance data for a Sela."""
    try:
        # Placeholder governance data
        return {
            'submitted_scrolls': [
                {
                    'title': 'Network Fee Reduction Proposal',
                    'category': 'Economic',
                    'status': 'approved',
                    'created_at': int(time.time()) - (7 * 24 * 3600)
                },
                {
                    'title': 'Validator Reward Enhancement',
                    'category': 'Tokenomics',
                    'status': 'pending',
                    'created_at': int(time.time()) - (3 * 24 * 3600)
                }
            ],
            'active_contracts': [
                {
                    'service_type': 'Technical Consulting',
                    'client_name': 'TechCorp Solutions',
                    'value': 150,
                    'progress': 75
                },
                {
                    'service_type': 'Blockchain Integration',
                    'client_name': 'StartupXYZ',
                    'value': 200,
                    'progress': 45
                }
            ]
        }
    except Exception as e:
        logger.error(f"Error getting governance data: {e}")
        return {'submitted_scrolls': [], 'active_contracts': []}

def get_sela_reputation(identity_id):
    """Get reputation data for a Sela."""
    try:
        # Get Etzem score if available
        etzem_score = db.query_one("""
            SELECT * FROM etzem_scores
            WHERE identity_id = ?
        """, (identity_id,))

        if etzem_score:
            return {
                'composite_score': etzem_score.get('composite_score', 7.5),
                'service_quality': etzem_score.get('service_quality', 8.2),
                'reliability': etzem_score.get('reliability', 7.8),
                'community_impact': etzem_score.get('community_impact', 6.9),
                'biblical_compliance': etzem_score.get('biblical_compliance', 8.5)
            }
        else:
            return {
                'composite_score': 7.5,
                'service_quality': 8.2,
                'reliability': 7.8,
                'community_impact': 6.9,
                'biblical_compliance': 8.5
            }
    except Exception as e:
        logger.error(f"Error getting reputation data: {e}")
        return {
            'composite_score': 0,
            'service_quality': 0,
            'reliability': 0,
            'community_impact': 0,
            'biblical_compliance': 0
        }

def get_sela_escrow(sela_id):
    """Get escrow data for a Sela."""
    try:
        # Placeholder escrow data
        return {
            'pending_count': 2,
            'recent_transactions': [
                {
                    'amount': 75,
                    'service_type': 'Consulting',
                    'status': 'pending',
                    'timeline': '3 days remaining'
                },
                {
                    'amount': 120,
                    'service_type': 'Development',
                    'status': 'released',
                    'timeline': 'Completed'
                },
                {
                    'amount': 50,
                    'service_type': 'Audit',
                    'status': 'escrow',
                    'timeline': '7 days remaining'
                }
            ]
        }
    except Exception as e:
        logger.error(f"Error getting escrow data: {e}")
        return {'pending_count': 0, 'recent_transactions': []}

@sela_bp.route('/')
def directory():
    """Validator Network directory page with real-time mining data."""
    try:
        # Get search parameters
        category = request.args.get('category', '')
        search = request.args.get('search', '')
        page = request.args.get('page', 1, type=int)
        per_page = 12
        offset = (page - 1) * per_page

        # Build query for validators (Selas with mining data)
        where_conditions = ["s.status = 'active'"]
        params = []

        if category:
            where_conditions.append("s.category = ?")
            params.append(category)

        if search:
            where_conditions.append("(s.name LIKE ? OR s.description LIKE ? OR i.name LIKE ?)")
            params.extend([f"%{search}%", f"%{search}%", f"%{search}%"])

        where_clause = " AND ".join(where_conditions)

        # Get Validators with mining information and token balances
        validators = db.query(f"""
            SELECT s.*, i.name as owner_name, i.email as owner_email,
                   m.mining_tier, m.mining_power, m.mining_id,
                   COALESCE(t.balance, 0) as onx_balance,
                   COALESCE(mining_stats.blocks_mined, 0) as blocks_mined,
                   COALESCE(mining_stats.total_rewards, 0) as total_rewards
            FROM selas s
            JOIN identities i ON s.identity_id = i.identity_id
            LEFT JOIN mining m ON s.sela_id = m.sela_id
            LEFT JOIN tokens t ON i.identity_id = t.identity_id AND t.token_id = 'ONX'
            LEFT JOIN (
                SELECT JSON_EXTRACT(data, '$.signed_by') as sela_id,
                       COUNT(*) as blocks_mined,
                       COUNT(*) * 10 as total_rewards
                FROM blocks
                WHERE JSON_EXTRACT(data, '$.signed_by') IS NOT NULL
                GROUP BY JSON_EXTRACT(data, '$.signed_by')
            ) mining_stats ON s.sela_id = mining_stats.sela_id
            WHERE {where_clause}
            ORDER BY m.mining_power DESC, s.created_at DESC
            LIMIT ? OFFSET ?
        """, params + [per_page, offset])

        # Parse metadata and add computed fields for each validator
        for validator in validators:
            try:
                validator['metadata_parsed'] = json.loads(validator.get('metadata', '{}'))
            except:
                validator['metadata_parsed'] = {}

            # Add mining tier display
            validator['mining_tier_display'] = validator.get('mining_tier', 'Basic CPU')
            validator['mining_power_display'] = f"{validator.get('mining_power', 1.0)}x"

            # Calculate validator performance
            validator['performance_score'] = min(100, (validator.get('blocks_mined', 0) * 10) +
                                                 (validator.get('trust_score', 0) * 20))

        # Get total count for pagination
        total_count = db.query_one(f"""
            SELECT COUNT(*) as count
            FROM selas s
            JOIN identities i ON s.identity_id = i.identity_id
            LEFT JOIN mining m ON s.sela_id = m.sela_id
            WHERE {where_clause}
        """, params)['count']

        # Get available categories
        categories = db.query("""
            SELECT DISTINCT category
            FROM selas
            WHERE status = 'active'
            ORDER BY category
        """)

        # Calculate pagination
        total_pages = (total_count + per_page - 1) // per_page

        # Get comprehensive network statistics
        network_stats = get_network_statistics()

        return render_template('sela/directory.html',
                             selas=validators,  # Pass as 'selas' for template compatibility
                             validators=validators,  # Also pass as 'validators' for clarity
                             categories=[c['category'] for c in categories],
                             current_category=category,
                             current_search=search,
                             page=page,
                             total_pages=total_pages,
                             total_count=total_count,
                             network_stats=network_stats,
                             # Legacy compatibility
                             total_blocks=network_stats['total_blocks'],
                             total_transactions=network_stats['total_transactions'])

    except Exception as e:
        logger.error(f"Error loading Validator directory: {e}")
        # Get basic network stats even on error
        network_stats = get_network_statistics()

        return render_template('sela/directory.html',
                             selas=[],
                             validators=[],
                             categories=[],
                             network_stats=network_stats,
                             total_blocks=network_stats['total_blocks'],
                             total_transactions=network_stats['total_transactions'],
                             error="Error loading validator directory")

def get_network_statistics():
    """Get comprehensive network statistics for the validator dashboard."""
    try:
        # Get basic counts
        total_blocks = db.query_one("SELECT COUNT(*) as count FROM blocks")['count']

        # Get mempool + confirmed transactions
        mempool_count = 0
        confirmed_count = 0
        try:
            mempool_count = db.query_one("SELECT COUNT(*) as count FROM mempool")['count']
        except:
            pass
        try:
            confirmed_count = db.query_one("SELECT COUNT(*) as count FROM transactions")['count']
        except:
            pass

        total_transactions = mempool_count + confirmed_count

        # Get active validators count
        active_validators = db.query_one("""
            SELECT COUNT(*) as count
            FROM selas s
            JOIN mining m ON s.sela_id = m.sela_id
            WHERE s.status = 'active'
        """)['count']

        # Calculate total mining power
        total_mining_power = db.query_one("""
            SELECT COALESCE(SUM(m.mining_power), 0) as total_power
            FROM selas s
            JOIN mining m ON s.sela_id = m.sela_id
            WHERE s.status = 'active'
        """)['total_power'] or 0

        # Calculate network uptime (simplified - based on recent blocks)
        try:
            recent_blocks = db.query_one("""
                SELECT COUNT(*) as count
                FROM blocks
                WHERE timestamp > ?
            """, (int(time.time()) - 86400,))['count']  # Last 24 hours

            # Simple uptime calculation: if we have blocks in last 24h, assume good uptime
            network_uptime = 99.9 if recent_blocks > 0 else 95.0
        except:
            network_uptime = 99.9

        # Get total rewards distributed
        total_rewards = db.query_one("""
            SELECT COALESCE(SUM(balance), 0) as total_rewards
            FROM tokens
            WHERE token_id = 'ONX'
        """)['total_rewards'] or 0

        return {
            'total_blocks': total_blocks,
            'total_transactions': total_transactions,
            'active_validators': active_validators,
            'total_mining_power': total_mining_power,
            'network_uptime': network_uptime,
            'total_rewards': total_rewards,
            'mempool_count': mempool_count,
            'confirmed_count': confirmed_count
        }

    except Exception as e:
        logger.error(f"Error getting network statistics: {e}")
        return {
            'total_blocks': 0,
            'total_transactions': 0,
            'active_validators': 0,
            'total_mining_power': 0,
            'network_uptime': 99.9,
            'total_rewards': 0,
            'mempool_count': 0,
            'confirmed_count': 0
        }

@sela_bp.route('/<sela_id>')
def profile(sela_id):
    """Public Sela profile page."""
    try:
        # Get Sela with owner information
        sela = db.query_one("""
            SELECT s.*, i.name as owner_name, i.email as owner_email, i.public_key
            FROM selas s
            JOIN identities i ON s.identity_id = i.identity_id
            WHERE s.sela_id = ?
        """, (sela_id,))

        if not sela:
            return render_template('errors/404.html'), 404

        # Parse metadata
        try:
            sela['metadata_parsed'] = json.loads(sela['metadata'])
        except:
            sela['metadata_parsed'] = {}

        # Get Sela's transactions
        sela_transactions = db.query("""
            SELECT * FROM transactions
            WHERE sender = ? OR data LIKE ?
            ORDER BY created_at DESC
            LIMIT 10
        """, (sela['identity_id'], f'%{sela_id}%'))

        # Parse transaction data
        for tx in sela_transactions:
            try:
                tx['data_parsed'] = json.loads(tx['data'])
            except:
                tx['data_parsed'] = {}

        # Get token balances if available
        token_balances = []
        if db.table_exists('token_balances'):
            token_balances = db.query("""
                SELECT tb.*, t.name, t.symbol, t.category
                FROM token_balances tb
                JOIN tokens t ON tb.token_id = t.token_id
                WHERE tb.identity_id = ?
                ORDER BY tb.balance DESC
            """, (sela['identity_id'],))

        # Get Etzem score if available
        etzem_score = None
        if db.table_exists('etzem_scores'):
            etzem_score = db.query_one("""
                SELECT * FROM etzem_scores
                WHERE identity_id = ?
            """, (sela['identity_id'],))

        # Calculate basic metrics
        metrics = {
            'total_transactions': len(sela_transactions),
            'total_tokens': sum(tb.get('balance', 0) for tb in token_balances),
            'trust_score': etzem_score.get('composite_score', 0) if etzem_score else 0,
            'registration_date': sela['created_at']
        }

        return render_template('sela/profile.html',
                             sela=sela,
                             transactions=sela_transactions,
                             token_balances=token_balances,
                             etzem_score=etzem_score,
                             metrics=metrics)

    except Exception as e:
        logger.error(f"Error loading Sela profile {sela_id}: {e}")
        return render_template('errors/500.html'), 500

@sela_bp.route('/<sela_id>/dashboard')
def dashboard(sela_id):
    """Comprehensive Sela dashboard with biblical tokenomics and covenant economics."""
    try:
        # Get Sela information with enhanced details
        sela = db.query_one("""
            SELECT s.*, i.name as founder_name, i.nation_code, i.nation_name
            FROM selas s
            LEFT JOIN identities i ON s.identity_id = i.identity_id
            WHERE s.sela_id = ?
        """, (sela_id,))

        if not sela:
            return render_template('errors/404.html'), 404

        # Parse metadata and add defaults
        try:
            sela['metadata_parsed'] = json.loads(sela['metadata'])
        except:
            sela['metadata_parsed'] = {}

        # Add covenant class and registration transaction
        sela['covenant_class'] = sela['metadata_parsed'].get('covenant_class', 'Harashim')
        sela['registration_tx'] = sela['metadata_parsed'].get('registration_tx', sela_id)

        # Get verification status
        verification = get_sela_verification_status(sela_id, sela['identity_id'])

        # Get accounting information
        accounting = get_sela_accounting(sela_id, sela['identity_id'])

        # Get participants and structure
        participants = get_sela_participants(sela_id)

        # Get service logs
        service_logs = get_sela_service_logs(sela_id)

        # Get sub-selas
        sub_selas = get_sub_selas(sela_id)

        # Get governance data
        governance = get_sela_governance(sela_id)

        # Get reputation data
        reputation = get_sela_reputation(sela['identity_id'])

        # Get escrow data
        escrow = get_sela_escrow(sela_id)

        return render_template('sela/dashboard.html',
                             sela=sela,
                             verification=verification,
                             accounting=accounting,
                             participants=participants,
                             service_logs=service_logs,
                             sub_selas=sub_selas,
                             governance=governance,
                             reputation=reputation,
                             escrow=escrow)

    except Exception as e:
        logger.error(f"Error loading Sela dashboard {sela_id}: {e}")
        return render_template('errors/500.html'), 500

@sela_bp.route('/<sela_id>/contact', methods=['POST'])
def contact(sela_id):
    """Handle contact form submission."""
    try:
        # Get form data
        name = request.form.get('name', '').strip()
        email = request.form.get('email', '').strip()
        message = request.form.get('message', '').strip()

        if not all([name, email, message]):
            return jsonify({'success': False, 'error': 'All fields are required'})

        # Get Sela information
        sela = db.query_one("SELECT * FROM selas WHERE sela_id = ?", (sela_id,))
        if not sela:
            return jsonify({'success': False, 'error': 'Sela not found'})

        # Log the contact attempt (in a real system, you'd send an email)
        contact_data = {
            'sela_id': sela_id,
            'contact_name': name,
            'contact_email': email,
            'message': message,
            'timestamp': int(time.time())
        }

        # Store in event logs
        db.insert('event_logs', {
            'event_type': 'sela_contact',
            'entity_id': sela_id,
            'data': json.dumps(contact_data),
            'timestamp': int(time.time())
        })

        logger.info(f"Contact form submitted for Sela {sela_id} by {email}")

        return jsonify({'success': True, 'message': 'Message sent successfully!'})

    except Exception as e:
        logger.error(f"Error handling contact form: {e}")
        return jsonify({'success': False, 'error': 'Failed to send message'})

@sela_bp.route('/api/search')
def api_search():
    """API endpoint for Sela search."""
    try:
        query = request.args.get('q', '').strip()
        limit = request.args.get('limit', 10, type=int)

        if not query:
            return jsonify([])

        # Search Selas
        results = db.query("""
            SELECT s.sela_id, s.name, s.category, i.name as owner_name
            FROM selas s
            JOIN identities i ON s.identity_id = i.identity_id
            WHERE s.status = 'active'
            AND (s.name LIKE ? OR s.category LIKE ? OR s.metadata LIKE ?)
            ORDER BY s.name
            LIMIT ?
        """, (f"%{query}%", f"%{query}%", f"%{query}%", limit))

        return jsonify(results)

    except Exception as e:
        logger.error(f"Error in Sela search API: {e}")
        return jsonify([])

# Sela Labor Management API Endpoints

@sela_bp.route('/api/<sela_id>/members')
def api_sela_members(sela_id):
    """Get Sela team members for labor management."""
    try:
        # Check if user has access to this Sela
        if 'identity_id' not in session:
            return jsonify({'error': 'Authentication required'}), 401

        # Get Sela details to verify ownership/access
        sela = db.query_one("""
            SELECT identity_id FROM selas WHERE sela_id = ?
        """, (sela_id,))

        if not sela:
            return jsonify({'error': 'Sela not found'}), 404

        # For now, allow access to Sela owner and members
        # In future, implement proper role-based access

        # Get team members (for now, return all identities as potential team members)
        members = db.query("""
            SELECT identity_id, name, nation_code, nation_name, role_class
            FROM identities
            WHERE status = 'active'
            ORDER BY name
            LIMIT 20
        """)

        return jsonify({'members': members})

    except Exception as e:
        logger.error(f"Error getting Sela members: {e}")
        return jsonify({'error': 'Failed to get members'}), 500

@sela_bp.route('/api/<sela_id>/labor-statistics')
def api_sela_labor_statistics(sela_id):
    """Get comprehensive labor statistics for a Sela."""
    try:
        # Check authentication
        if 'identity_id' not in session:
            return jsonify({'error': 'Authentication required'}), 401

        # Get team statistics
        team_stats = db.query_one("""
            SELECT
                COUNT(*) as total_labor_count,
                COALESCE(SUM(value_estimate), 0) as total_value_created,
                COALESCE(SUM(etzem_points), 0) as total_etzem_earned,
                COUNT(CASE WHEN verification_status = 'verified' THEN 1 END) as verified_count,
                COALESCE(AVG(CASE WHEN verification_status = 'verified' THEN value_estimate END), 0) as avg_verified_value
            FROM labor_records
            WHERE sela_id = ?
        """, (sela_id,))

        # Calculate verification rate
        verification_rate = 0
        if team_stats and team_stats['total_labor_count'] > 0:
            verification_rate = (team_stats['verified_count'] / team_stats['total_labor_count']) * 100

        # Get recent team labor
        recent_labor = db.query("""
            SELECT lr.*, i.name as member_name
            FROM labor_records lr
            JOIN identities i ON lr.identity_id = i.identity_id
            WHERE lr.sela_id = ?
            ORDER BY lr.timestamp DESC
            LIMIT 10
        """, (sela_id,))

        # Get member details with labor statistics
        member_details = db.query("""
            SELECT
                i.identity_id,
                i.name,
                i.nation_code,
                i.nation_name,
                i.role_class as role,
                COUNT(lr.labor_id) as labor_count,
                COALESCE(SUM(lr.value_estimate), 0) as value_created,
                COALESCE(SUM(lr.etzem_points), 0) as tokens_earned,
                COUNT(CASE WHEN lr.verification_status = 'verified' THEN 1 END) as verified_count
            FROM identities i
            LEFT JOIN labor_records lr ON i.identity_id = lr.identity_id AND lr.sela_id = ?
            WHERE i.identity_id IN (
                SELECT DISTINCT identity_id FROM labor_records WHERE sela_id = ?
            )
            GROUP BY i.identity_id, i.name, i.nation_code, i.nation_name, i.role_class
            ORDER BY value_created DESC
        """, (sela_id, sela_id))

        # Calculate verification rates for each member
        for member in member_details:
            if member['labor_count'] > 0:
                member['verification_rate'] = (member['verified_count'] / member['labor_count']) * 100
            else:
                member['verification_rate'] = 0

        # Calculate pending distribution (simplified)
        pending_distribution = (team_stats['total_etzem_earned'] or 0) * 0.1  # 10% of Etzem as tokens

        return jsonify({
            'team_statistics': {
                'total_labor_count': team_stats['total_labor_count'] if team_stats else 0,
                'total_value_created': team_stats['total_value_created'] if team_stats else 0,
                'total_mikvah_earned': (team_stats['total_etzem_earned'] or 0) * 0.1 if team_stats else 0,
                'verification_rate': verification_rate,
                'pending_distribution': pending_distribution
            },
            'recent_labor': recent_labor,
            'member_details': member_details
        })

    except Exception as e:
        logger.error(f"Error getting Sela labor statistics: {e}")
        return jsonify({'error': 'Failed to get labor statistics'}), 500

@sela_bp.route('/api/<sela_id>/pending-verifications')
def api_sela_pending_verifications(sela_id):
    """Get pending labor verifications for Sela team."""
    try:
        # Check authentication
        if 'identity_id' not in session:
            return jsonify({'error': 'Authentication required'}), 401

        # Get pending labor for this Sela
        pending_labor = db.query("""
            SELECT lr.*, i.name as submitter_name
            FROM labor_records lr
            JOIN identities i ON lr.identity_id = i.identity_id
            WHERE lr.sela_id = ? AND lr.verification_status = 'pending'
            ORDER BY lr.timestamp DESC
            LIMIT 20
        """, (sela_id,))

        return jsonify({'pending_labor': pending_labor})

    except Exception as e:
        logger.error(f"Error getting pending verifications: {e}")
        return jsonify({'error': 'Failed to get pending verifications'}), 500

@sela_bp.route('/api/<sela_id>/distribute-tokens', methods=['POST'])
def api_distribute_tokens(sela_id):
    """Distribute tokens to all team members."""
    try:
        # Check authentication
        if 'identity_id' not in session:
            return jsonify({'error': 'Authentication required'}), 401

        requesting_identity = session['identity_id']

        # Check if user is Sela owner or has permission
        sela = db.query_one("""
            SELECT identity_id FROM selas WHERE sela_id = ?
        """, (sela_id,))

        if not sela or sela['identity_id'] != requesting_identity:
            return jsonify({'error': 'Insufficient permissions'}), 403

        # Get team members with verified labor
        team_members = db.query("""
            SELECT
                lr.identity_id,
                i.name,
                COUNT(lr.labor_id) as labor_count,
                COALESCE(SUM(lr.etzem_points), 0) as total_etzem
            FROM labor_records lr
            JOIN identities i ON lr.identity_id = i.identity_id
            WHERE lr.sela_id = ? AND lr.verification_status = 'verified'
            GROUP BY lr.identity_id, i.name
            HAVING total_etzem > 0
        """, (sela_id,))

        if not team_members:
            return jsonify({'error': 'No team members with verified labor found'}), 400

        # Calculate distribution (simplified - equal distribution for now)
        total_to_distribute = sum(member['total_etzem'] for member in team_members) * 0.1
        distribution_per_member = total_to_distribute / len(team_members)

        # Import tokenomics system
        from blockchain.tokenomics.mikvah_tokens import MikvahTokenManager
        mikvah_manager = MikvahTokenManager(db)

        distributed_count = 0
        total_distributed = 0

        for member in team_members:
            try:
                result = mikvah_manager.process_token_reward(
                    identity_id=member['identity_id'],
                    amount=distribution_per_member,
                    transaction_type='sela_distribution',
                    metadata={
                        'sela_id': sela_id,
                        'distribution_type': 'team_distribution',
                        'labor_count': member['labor_count']
                    }
                )

                if result['success']:
                    distributed_count += 1
                    total_distributed += distribution_per_member

            except Exception as e:
                logger.error(f"Error distributing to {member['identity_id']}: {e}")
                continue

        return jsonify({
            'success': True,
            'total_distributed': round(total_distributed, 2),
            'member_count': distributed_count,
            'message': f'Successfully distributed {total_distributed:.2f} tokens to {distributed_count} team members'
        })

    except Exception as e:
        logger.error(f"Error distributing tokens: {e}")
        return jsonify({'error': 'Failed to distribute tokens'}), 500

@sela_bp.route('/api/<sela_id>/distribute-member-tokens', methods=['POST'])
def api_distribute_member_tokens(sela_id):
    """Distribute tokens to a specific team member."""
    try:
        # Check authentication
        if 'identity_id' not in session:
            return jsonify({'error': 'Authentication required'}), 401

        requesting_identity = session['identity_id']

        # Check if user is Sela owner
        sela = db.query_one("""
            SELECT identity_id FROM selas WHERE sela_id = ?
        """, (sela_id,))

        if not sela or sela['identity_id'] != requesting_identity:
            return jsonify({'error': 'Insufficient permissions'}), 403

        # Get request data
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        member_identity_id = data.get('identity_id')
        amount = data.get('amount', 0)

        if not member_identity_id or amount <= 0:
            return jsonify({'error': 'Invalid member or amount'}), 400

        # Verify member has labor for this Sela
        member_labor = db.query_one("""
            SELECT COUNT(*) as count FROM labor_records
            WHERE identity_id = ? AND sela_id = ?
        """, (member_identity_id, sela_id))

        if not member_labor or member_labor['count'] == 0:
            return jsonify({'error': 'Member has no labor records for this Sela'}), 400

        # Import tokenomics system
        from blockchain.tokenomics.mikvah_tokens import MikvahTokenManager
        mikvah_manager = MikvahTokenManager(db)

        # Process token distribution
        result = mikvah_manager.process_token_reward(
            identity_id=member_identity_id,
            amount=amount,
            transaction_type='sela_member_distribution',
            metadata={
                'sela_id': sela_id,
                'distribution_type': 'individual_payment',
                'distributed_by': requesting_identity
            }
        )

        if result['success']:
            return jsonify({
                'success': True,
                'amount': amount,
                'transaction_id': result['transaction_id'],
                'message': f'Successfully distributed {amount} tokens'
            })
        else:
            return jsonify({'error': result.get('error', 'Distribution failed')}), 400

    except Exception as e:
        logger.error(f"Error distributing member tokens: {e}")
        return jsonify({'error': 'Failed to distribute tokens'}), 500

@sela_bp.route('/api/<sela_id>/labor-report', methods=['POST'])
def api_generate_labor_report(sela_id):
    """Generate a labor report for the Sela."""
    try:
        # Check authentication
        if 'identity_id' not in session:
            return jsonify({'error': 'Authentication required'}), 401

        # For now, return a simple JSON report
        # In future, could generate PDF reports

        # Get comprehensive labor data
        labor_data = db.query("""
            SELECT
                lr.*,
                i.name as member_name,
                i.nation_name
            FROM labor_records lr
            JOIN identities i ON lr.identity_id = i.identity_id
            WHERE lr.sela_id = ?
            ORDER BY lr.timestamp DESC
        """, (sela_id,))

        # Get summary statistics
        summary = db.query_one("""
            SELECT
                COUNT(*) as total_labor,
                COUNT(CASE WHEN verification_status = 'verified' THEN 1 END) as verified_labor,
                COALESCE(SUM(value_estimate), 0) as total_value,
                COALESCE(SUM(etzem_points), 0) as total_etzem
            FROM labor_records
            WHERE sela_id = ?
        """, (sela_id,))

        report = {
            'sela_id': sela_id,
            'generated_at': int(time.time()),
            'summary': summary,
            'labor_records': labor_data,
            'report_type': 'comprehensive_labor_report'
        }

        return jsonify(report)

    except Exception as e:
        logger.error(f"Error generating labor report: {e}")
        return jsonify({'error': 'Failed to generate report'}), 500
