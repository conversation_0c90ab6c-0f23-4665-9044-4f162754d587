#!/usr/bin/env python3
"""
Test CIPP API Endpoints
Tests the Covenant Identity Protection Protocol API functionality
"""

import requests
import json
import time

BASE_URL = "http://127.0.0.1:5000"

def test_nations_api():
    """Test the biblical nations API."""
    print("🏛️ Testing Biblical Nations API...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/nations/list")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Nations API: {data['total_count']} nations available")
            
            # Show first few nations
            for nation in data['nations'][:3]:
                print(f"  {nation['flag_symbol']} {nation['nation_name']} ({nation['tribe_name']})")
        else:
            print(f"❌ Nations API failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Nations API error: {e}")

def test_protection_status_api():
    """Test protection status API."""
    print("🛡️ Testing Protection Status API...")
    
    # Get an existing identity ID
    try:
        from shared.db.db import db
        identity = db.query_one("SELECT identity_id FROM identities LIMIT 1")
        
        if not identity:
            print("❌ No identities found for testing")
            return
            
        identity_id = identity['identity_id']
        
        response = requests.get(f"{BASE_URL}/api/identity/{identity_id}/protection-status")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Protection Status API: Tier {data['protection_tier']}, Vault {data['vault_status']}")
        else:
            print(f"❌ Protection Status API failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Protection Status API error: {e}")

def test_verification_progress_api():
    """Test verification progress API."""
    print("📊 Testing Verification Progress API...")
    
    try:
        from shared.db.db import db
        identity = db.query_one("SELECT identity_id FROM identities LIMIT 1")
        
        if not identity:
            print("❌ No identities found for testing")
            return
            
        identity_id = identity['identity_id']
        
        response = requests.get(f"{BASE_URL}/api/identity/{identity_id}/verification-progress")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Verification Progress API: Current Tier {data['current_tier']}")
            print(f"  Tier Progress: {data['tier_progress']}")
        else:
            print(f"❌ Verification Progress API failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Verification Progress API error: {e}")

def test_covenant_acceptance_api():
    """Test covenant acceptance API."""
    print("📜 Testing Covenant Acceptance API...")
    
    try:
        from shared.db.db import db
        identity = db.query_one("SELECT identity_id FROM identities LIMIT 1")
        
        if not identity:
            print("❌ No identities found for testing")
            return
            
        identity_id = identity['identity_id']
        
        # Test covenant acceptance (should fail if already accepted)
        payload = {
            "version": "v1.0",
            "signature_hash": "test_signature_hash"
        }
        
        response = requests.post(
            f"{BASE_URL}/api/identity/{identity_id}/accept-covenant",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Covenant Acceptance API: {data['message']}")
        elif response.status_code == 400:
            data = response.json()
            print(f"ℹ️ Covenant Acceptance API: {data['error']} (expected if already accepted)")
        else:
            print(f"❌ Covenant Acceptance API failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Covenant Acceptance API error: {e}")

def test_protection_request_api():
    """Test protection request API."""
    print("🚨 Testing Protection Request API...")
    
    try:
        from shared.db.db import db
        identity = db.query_one("SELECT identity_id FROM identities LIMIT 1")
        
        if not identity:
            print("❌ No identities found for testing")
            return
            
        identity_id = identity['identity_id']
        
        # Test protection request
        payload = {
            "type": "EMERGENCY_FREEZE",
            "reason": "API test protection request"
        }
        
        response = requests.post(
            f"{BASE_URL}/api/identity/{identity_id}/request-protection",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Protection Request API: {data['message']}")
            print(f"  Request ID: {data['request_id']}")
        else:
            print(f"❌ Protection Request API failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Protection Request API error: {e}")

def test_web_pages():
    """Test CIPP web pages."""
    print("🌐 Testing CIPP Web Pages...")
    
    pages = [
        ("/auth/register/identity", "Identity Registration"),
        ("/auth/covenant-scroll", "Digital Scroll of Rights"),
        ("/", "Home Page")
    ]
    
    for path, name in pages:
        try:
            response = requests.get(f"{BASE_URL}{path}")
            if response.status_code == 200:
                print(f"✅ {name}: Page loads successfully")
                
                # Check for CIPP-specific content
                content = response.text.lower()
                if "covenant" in content or "biblical" in content or "cipp" in content:
                    print(f"  ✅ Contains CIPP content")
                else:
                    print(f"  ⚠️ May not contain CIPP content")
            else:
                print(f"❌ {name}: Failed to load ({response.status_code})")
                
        except Exception as e:
            print(f"❌ {name}: Error loading page - {e}")

def main():
    """Main test function."""
    print("🛡️ Starting CIPP API and Web Tests...")
    print("=" * 50)
    
    # Wait for server to be ready
    print("⏳ Waiting for server to be ready...")
    time.sleep(2)
    
    # Test API endpoints
    test_nations_api()
    print()
    
    test_protection_status_api()
    print()
    
    test_verification_progress_api()
    print()
    
    test_covenant_acceptance_api()
    print()
    
    test_protection_request_api()
    print()
    
    # Test web pages
    test_web_pages()
    print()
    
    print("=" * 50)
    print("🎉 CIPP Testing Complete!")

if __name__ == '__main__':
    main()
