#!/usr/bin/env python3
"""
ONNYX Genesis Deployment Verification Script
Comprehensive verification of the complete genesis implementation
"""

import sys
import os
import json
import hashlib
import time

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

def verify_genesis_nations_config():
    """Verify the genesis nations configuration file."""
    print("🌍 Verifying Genesis Nations Configuration...")
    
    try:
        with open('data/genesis_nations.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        total_nations = config.get('total_nations', 0)
        covenant_nations = config.get('covenant_nations', 0)
        witness_nations = config.get('witness_nations', 0)
        
        print(f"  ✅ Total nations: {total_nations}")
        print(f"  ✅ Covenant nations: {covenant_nations}")
        print(f"  ✅ Witness nations: {witness_nations}")
        
        if total_nations == 47 and covenant_nations == 12 and witness_nations == 35:
            print("  🎉 Genesis nations configuration is correct!")
            return True
        else:
            print(f"  ❌ Expected 47 total (12 covenant + 35 witness), got {total_nations}")
            return False
            
    except Exception as e:
        print(f"  ❌ Error loading genesis nations config: {e}")
        return False

def verify_covenant_document():
    """Verify the Genesis Covenant document exists and calculate its hash."""
    print("📜 Verifying Genesis Covenant Document...")
    
    try:
        with open('docs/Genesis Covenant of Onnyx.md', 'r', encoding='utf-8') as f:
            covenant_text = f.read()
        
        covenant_hash = hashlib.sha3_256(covenant_text.encode('utf-8')).hexdigest()
        
        print(f"  ✅ Covenant document found")
        print(f"  ✅ Document length: {len(covenant_text)} characters")
        print(f"  ✅ SHA3-256 hash: {covenant_hash[:16]}...")
        
        # Check if document contains all required sections
        required_sections = [
            "Nation Initialization",
            "The Twelve Tribes of Israel",
            "The Dukes of Edom",
            "The Princes of Ishmael",
            "The Hamite Nations",
            "The Moabite Clans",
            "The Ammonite Clans",
            "The Philistine Cities"
        ]
        
        missing_sections = []
        for section in required_sections:
            if section not in covenant_text:
                missing_sections.append(section)
        
        if missing_sections:
            print(f"  ⚠️ Missing sections: {missing_sections}")
            return False, covenant_hash
        else:
            print("  ✅ All required sections present")
            return True, covenant_hash
            
    except Exception as e:
        print(f"  ❌ Error reading covenant document: {e}")
        return False, None

def verify_voice_scrolls_config():
    """Verify the genesis voice scrolls configuration."""
    print("📋 Verifying Genesis Voice Scrolls Configuration...")
    
    try:
        with open('data/genesis_voice_scrolls.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        scrolls = config.get('scrolls', [])
        print(f"  ✅ Found {len(scrolls)} genesis scrolls")
        
        expected_scrolls = [
            "SCROLL_001_COUNCIL_RATIFICATION",
            "SCROLL_002_GOVERNANCE_QUORUM", 
            "SCROLL_003_BIBLICAL_TOKENOMICS"
        ]
        
        found_scrolls = [scroll['scroll_id'] for scroll in scrolls]
        
        for expected in expected_scrolls:
            if expected in found_scrolls:
                print(f"  ✅ {expected}")
            else:
                print(f"  ❌ Missing: {expected}")
                return False
        
        print("  🎉 All genesis voice scrolls configured!")
        return True
        
    except Exception as e:
        print(f"  ❌ Error loading voice scrolls config: {e}")
        return False

def verify_onboarding_questions():
    """Verify the covenant onboarding questions."""
    print("❓ Verifying Covenant Onboarding Questions...")
    
    try:
        with open('docs/onboarding_questions.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        test = config.get('covenant_comprehension_test', {})
        questions = test.get('questions', [])
        
        print(f"  ✅ Found {len(questions)} onboarding questions")
        
        categories = set()
        for question in questions:
            categories.add(question.get('category', 'unknown'))
        
        expected_categories = {
            'psalm_83_enemies',
            'biblical_commerce', 
            'labor_tokenomics',
            'identity_protection',
            'governance_structure'
        }
        
        if categories == expected_categories:
            print("  ✅ All question categories covered")
            print(f"  ✅ Passing score: {test.get('passing_score', 0)}/{test.get('total_questions', 0)}")
            return True
        else:
            missing = expected_categories - categories
            print(f"  ❌ Missing categories: {missing}")
            return False
            
    except Exception as e:
        print(f"  ❌ Error loading onboarding questions: {e}")
        return False

def verify_opcodes_implementation():
    """Verify that all required opcodes are implemented."""
    print("⚙️ Verifying Opcodes Implementation...")
    
    try:
        from blockchain.vm.opcodes import (
            OP_MINT, OP_SEND, OP_IDENTITY, OP_SCROLL, OP_BURN,
            OP_GRANT_REPUTATION, OP_STAKE, OP_VOTE, OP_REWARD,
            OP_JUBILEE, OP_LEND, OP_REPAY, OP_FORGIVE,
            OP_FIRSTFRUITS, OP_GLEANING_CLAIM, OP_COVENANT_FOUNDING,
            OPCODES
        )
        
        core_opcodes = [
            OP_MINT, OP_SEND, OP_IDENTITY, OP_SCROLL, OP_BURN,
            OP_GRANT_REPUTATION, OP_STAKE, OP_VOTE, OP_REWARD
        ]
        
        biblical_opcodes = [
            OP_JUBILEE, OP_LEND, OP_REPAY, OP_FORGIVE,
            OP_FIRSTFRUITS, OP_GLEANING_CLAIM
        ]
        
        genesis_opcodes = [OP_COVENANT_FOUNDING]
        
        print(f"  ✅ Core opcodes: {len(core_opcodes)}")
        print(f"  ✅ Biblical opcodes: {len(biblical_opcodes)}")
        print(f"  ✅ Genesis opcodes: {len(genesis_opcodes)}")
        
        total_expected = len(core_opcodes) + len(biblical_opcodes) + len(genesis_opcodes)
        total_implemented = len(OPCODES)
        
        if total_implemented >= total_expected:
            print(f"  ✅ All {total_expected} required opcodes implemented")
            return True
        else:
            print(f"  ❌ Expected {total_expected} opcodes, found {total_implemented}")
            return False
            
    except Exception as e:
        print(f"  ❌ Error verifying opcodes: {e}")
        return False

def verify_database_readiness():
    """Verify the database is ready for genesis deployment."""
    print("🗄️ Verifying Database Readiness...")
    
    try:
        from shared.db.db import db
        
        # Check required tables exist
        required_tables = [
            'blocks', 'transactions', 'identities', 'biblical_nations',
            'voice_scrolls', 'labor_records', 'mikvah_transactions',
            'verification_progress', 'covenant_acceptances'
        ]
        
        for table in required_tables:
            try:
                result = db.query_one(f"SELECT COUNT(*) as count FROM {table}")
                print(f"  ✅ {table}: {result['count']} records")
            except Exception as e:
                print(f"  ❌ {table}: {e}")
                return False
        
        print("  🎉 Database is ready for genesis deployment!")
        return True
        
    except Exception as e:
        print(f"  ❌ Database verification error: {e}")
        return False

def verify_biblical_tokenomics():
    """Verify biblical tokenomics system is operational."""
    print("🪙 Verifying Biblical Tokenomics System...")
    
    try:
        from blockchain.tokenomics.biblical_tokenomics import BiblicalTokenomics
        from blockchain.tokenomics.mikvah_tokens import MikvahTokenManager
        from shared.db.db import db
        
        bt = BiblicalTokenomics(db)
        mm = MikvahTokenManager(db)
        
        print(f"  ✅ Current Yovel cycle: {bt.current_yovel_cycle}")
        print(f"  ✅ Current season: {bt.current_season}")
        print(f"  ✅ Sabbath enforcement: {'Active' if bt.is_sabbath_period() else 'Inactive'}")
        print(f"  ✅ Mikvah token manager: Operational")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Biblical tokenomics verification error: {e}")
        return False

def main():
    """Main verification function."""
    print("🌟 ONNYX GENESIS DEPLOYMENT VERIFICATION")
    print("=" * 60)
    
    results = []
    
    # Run all verifications
    results.append(verify_genesis_nations_config())
    covenant_ok, covenant_hash = verify_covenant_document()
    results.append(covenant_ok)
    results.append(verify_voice_scrolls_config())
    results.append(verify_onboarding_questions())
    results.append(verify_opcodes_implementation())
    results.append(verify_database_readiness())
    results.append(verify_biblical_tokenomics())
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 VERIFICATION SUMMARY")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"✅ Passed: {passed}/{total} ({(passed/total)*100:.1f}%)")
    
    if passed == total:
        print("🎉 ALL VERIFICATIONS PASSED - READY FOR GENESIS DEPLOYMENT!")
        print("\n📋 Next Steps:")
        print("1. Run: python scripts/generate_genesis_block.py")
        print("2. Verify genesis block creation")
        print("3. Begin tribal elder onboarding")
        print("4. Deploy genesis voice scrolls")
        print("5. Activate governance system")
        
        if covenant_hash:
            print(f"\n🔐 Covenant Hash: {covenant_hash}")
            print("This hash will be permanently sealed in the genesis block.")
    else:
        print("⚠️ SOME VERIFICATIONS FAILED - REVIEW REQUIRED")
        print("\nPlease address the failed verifications before proceeding with genesis deployment.")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
